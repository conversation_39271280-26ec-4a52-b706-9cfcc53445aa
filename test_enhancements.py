#!/usr/bin/env python3
"""
Test script to verify Parlant-inspired enhancements to QualityX.py
"""

import sys
import json

# Import the enhanced components from QualityX
try:
    from QualityX import (
        PromptBuilder, 
        OptimizationPolicy, 
        validate_llm_text,
        format_financial_data,
        format_valuation_data,
        format_economic_data,
        log_prompt_sections
    )
    print("✓ Successfully imported enhanced components from QualityX.py")
except ImportError as e:
    print(f"✗ Failed to import components: {e}")
    sys.exit(1)

def test_prompt_builder():
    """Test enhanced PromptBuilder functionality"""
    print("\n=== Testing PromptBuilder ===")
    
    # Test basic functionality
    builder = PromptBuilder(context_name="test_context")
    builder.add_context("TEST_CONTEXT", "This is test context")
    builder.add_data_section("TEST_DATA", "Sample data", purpose="Testing data sections")
    builder.add_instructions("TEST_INSTRUCTIONS", "Follow these instructions")
    
    prompt = builder.build()
    
    assert "TEST_CONTEXT" in prompt, "Context section missing"
    assert "TEST_DATA" in prompt, "Data section missing"
    assert "TEST_INSTRUCTIONS" in prompt, "Instructions section missing"
    assert "Purpose: Testing data sections" in prompt, "Purpose statement missing"
    
    # Test section metadata
    assert builder.get_section_count() == 3, f"Expected 3 sections, got {builder.get_section_count()}"
    section_types = builder.get_section_types()
    assert section_types.get('context') == 1, "Context type count incorrect"
    assert section_types.get('data') == 1, "Data type count incorrect"
    assert section_types.get('instructions') == 1, "Instructions type count incorrect"
    
    print("✓ PromptBuilder tests passed")

def test_optimization_policy():
    """Test OptimizationPolicy temperature progression"""
    print("\n=== Testing OptimizationPolicy ===")
    
    # Test conservative strategy
    temps_conservative = OptimizationPolicy.get_temperature_ladder(3, "conservative")
    assert len(temps_conservative) == 3, "Temperature ladder length incorrect"
    assert temps_conservative[0] < temps_conservative[1] < temps_conservative[2], "Temperature not increasing"
    assert all(0 <= t <= 1 for t in temps_conservative), "Temperature out of range"
    
    # Test moderate strategy
    temps_moderate = OptimizationPolicy.get_temperature_ladder(3, "moderate")
    assert temps_moderate[0] < temps_conservative[0] or temps_moderate[-1] > temps_conservative[-1], \
        "Moderate strategy should differ from conservative"
    
    # Test aggressive strategy
    temps_aggressive = OptimizationPolicy.get_temperature_ladder(3, "aggressive")
    assert temps_aggressive[-1] >= temps_moderate[-1], "Aggressive should have higher final temp"
    
    print("✓ OptimizationPolicy tests passed")

def test_validation():
    """Test enhanced validate_llm_text function"""
    print("\n=== Testing validate_llm_text ===")
    
    # Test valid output
    valid_text = """
1. SUSTAINABLE COMPETITIVE ADVANTAGE (ECONOMIC MOAT):
   Buffett Score: 45
2. INNOVATION CAPACITY & R&D EFFECTIVENESS:
   Buffett Score: 32
3. CORPORATE GOVERNANCE & ETHICS:
   Buffett Score: 58
4. FINANCIAL REPORTING QUALITY & TRANSPARENCY:
   Buffett Score: 41
5. COMPETITIVE POSITION & PRICING POWER:
   Buffett Score: 37
6. WONDERFUL BUSINESSES AT FAIR PRICES:
   Buffett Score: 29

[llm_qualitative_score]
   -Rating: 38.42
   llm_qualitative_score: 38.42/100
"""
    
    valid, reason = validate_llm_text(valid_text, strict=True)
    assert valid, f"Valid text failed validation: {reason}"
    
    # Test invalid output (missing score)
    invalid_text = """
1. SUSTAINABLE COMPETITIVE ADVANTAGE (ECONOMIC MOAT):
   Buffett Score: 45
2. INNOVATION CAPACITY & R&D EFFECTIVENESS:
   Buffett Score: 32
"""
    
    valid, reason = validate_llm_text(invalid_text, strict=True)
    assert not valid, "Invalid text passed validation"
    assert "Expected 6 'Buffett Score:' occurrences" in reason, f"Wrong error message: {reason}"
    
    # Test out of range score
    invalid_range = valid_text.replace("38.42", "150.00")
    valid, reason = validate_llm_text(invalid_range, strict=True)
    assert not valid, "Out of range score passed validation"
    
    print("✓ validate_llm_text tests passed")

def test_data_formatting():
    """Test data formatting helpers"""
    print("\n=== Testing Data Formatting Helpers ===")
    
    # Test format_financial_data
    test_data = {"revenue": 1000000, "net_income": 50000}
    formatted = format_financial_data(test_data)
    assert "revenue" in formatted, "Financial data formatting failed"
    assert "1000000" in formatted, "Financial data values missing"
    
    # Test format_valuation_data
    valuation_data = {
        "market_cap": 1000000000,
        "pe_ratio": 25.5,
        "pb_ratio": 3.2
    }
    formatted_val = format_valuation_data(valuation_data)
    assert "1,000,000,000" in formatted_val, "Market cap formatting failed"
    assert "25.50" in formatted_val, "PE ratio formatting failed"
    
    # Test format_economic_data
    fred_data = {
        "GDP": [{"date": "2024-01-01", "value": 2.5}],
        "Unemployment": [{"date": "2024-01-01", "value": 3.7}]
    }
    formatted_econ = format_economic_data(fred_data)
    assert "GDP" in formatted_econ, "Economic data formatting failed"
    assert "2024-01-01" in formatted_econ, "Economic data dates missing"
    
    # Test empty data handling
    assert format_financial_data(None) == "N/A", "Empty financial data not handled"
    assert format_valuation_data(None) == "No valuation metrics available", "Empty valuation data not handled"
    assert format_economic_data(None) == "No FRED economic data available.", "Empty economic data not handled"
    
    print("✓ Data formatting tests passed")

def main():
    """Run all tests"""
    print("=" * 60)
    print("Testing Parlant-Inspired Enhancements to QualityX.py")
    print("=" * 60)
    
    try:
        test_prompt_builder()
        test_optimization_policy()
        test_validation()
        test_data_formatting()
        
        print("\n" + "=" * 60)
        print("✓ ALL TESTS PASSED")
        print("=" * 60)
        print("\nEnhancements are working correctly!")
        print("The following Parlant-inspired patterns have been successfully integrated:")
        print("  • Enhanced PromptBuilder with structured context methods")
        print("  • OptimizationPolicy for temperature progression")
        print("  • Comprehensive LLM output validation")
        print("  • Standardized data formatting helpers")
        print("\nQualityX.py is ready to use with improved prompt engineering!")
        
        return 0
        
    except AssertionError as e:
        print(f"\n✗ TEST FAILED: {e}")
        return 1
    except Exception as e:
        print(f"\n✗ UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

