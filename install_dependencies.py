#!/usr/bin/env python3
"""
Script to automatically install Python dependencies from requirements.txt
"""

import subprocess
import sys
import os


def install_dependencies():
    """
    Install dependencies from requirements.txt using pip
    """
    # Check if requirements.txt exists
    if not os.path.exists('requirements.txt'):
        print("Error: requirements.txt file not found in the current directory.")
        return False

    print("Installing Python dependencies from requirements.txt...")
    print("-" * 50)

    try:
        # Run pip install command
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], capture_output=True, text=True, check=True)

        # Display stdout if there's any content
        if result.stdout:
            print("Installation output:")
            print(result.stdout)

        print("Dependencies installed successfully!")
        return True

    except subprocess.CalledProcessError as e:
        print(f"Error occurred during installation:")
        print(f"Return code: {e.returncode}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False

    except FileNotFoundError:
        print("Error: pip is not installed or not found in the system PATH.")
        return False

    except Exception as e:
        print(f"Unexpected error occurred: {str(e)}")
        return False


def main():
    """
    Main function to run the dependency installation
    """
    print("Python Dependency Installer")
    print("=" * 30)

    success = install_dependencies()

    if success:
        print("\nAll dependencies have been installed successfully.")
        print("You can now run QualityX.py")
    else:
        print("\nFailed to install dependencies. Please check the error messages above.")
        sys.exit(1)


if __name__ == "__main__":
    main()