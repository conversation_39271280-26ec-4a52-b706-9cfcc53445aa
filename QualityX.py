#!/usr/bin/env python3
"""
This script downloads financial data from Polygon.io API (/vX endpoint),
calculates quality metrics using available data points (attempting shares-based calcs),
fetches economic data from FRED, sends a prompt to a language model for qualitative analysis,
and saves the resulting report based on quantitative and qualitative scores.
"""

import os
import time
import json
import re
import requests
import concurrent.futures
from datetime import datetime
from dateutil.parser import parse
from dateutil.relativedelta import relativedelta
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import yfinance as yf

# Load environment variables from a .env file
load_dotenv()

# --- Configuration ---
class Config:
    COMPANIES = ["AAPL", "ABNB", "ADBE", "ADSK", "AMAT", "AMD", "AMZN", "ANET", "APH", "APP", "ARES", "AVGO", "AXON", "AZO", "BKNG", "BLDR", "BX", "CDNS", "CMG", "CPAY", "CPNG", "CPRT", "CRWD", "CTRA", "DDOG", "DECK", "DHI", "DPZ", "DT", "DUOL", "DVN", "DXCM", "ENTG", "ERIE", "EXE", "FICO", "FIX", "FTNT", "GDDY", "GOOGL", "HEI", "HUBS", "IDXX", "INTU", "ISRG", "IT", "JBHT", "KLAC", "LII", "LLY", "LPLA", "LRCX", "LYV", "MA", "MANH", "MCO", "MDB", "META", "MNST", "MOH", "MORN", "MPWR", "MRNA", "MSCI", "MSFT", "NET", "NFLX", "NOW", "NVDA", "NVR", "ODFL", "ON", "ORLY", "PANW", "PHM", "PINS", "PLTR", "PODD", "ROL", "SAIA", "SBUX", "SNPS", "TEAM", "TOST", "TPL", "TRGP", "TSCO", "TSLA", "TTD", "UPS", "V", "VEEV", "VRT", "VRTX", "VST", "WDAY", "WST", "YUM", "ZS"] # Example companies
    REQUEST_DELAY = 13  # Delay (seconds) for Polygon API (5 requests/min limit)
    INTER_COMPANY_DELAY = 0 # Delay between processing different companies sequentially
    MODEL_NAME = "deepseek/deepseek-chat-v3.1:free" # Example LLM
    POLYGON_BASE_URL = "https://api.polygon.io"
    POLYGON_API_KEY  = os.getenv("POLYGON_API_KEY")
    MAX_RETRIES = 4 # Max retries for HTTP requests
    OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
    OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
    MAX_WORKERS = 1 # Set to 1 for sequential processing (RECOMMENDED for free tier APIs)
    LLM_TIMEOUT = 120 # Timeout for LLM API calls (seconds)
    LLM_RETRIES = 3 # Retries for LLM calls
    LLM_RETRY_DELAY = 20 # Delay between LLM retries (seconds)
    DETAILED_LOGGING = False # Set to True for verbose calculation and API logging

    # Sector-Specific Adjustments Configuration
    ENABLE_SECTOR_ADJUSTMENTS = True # Set to False to disable sector-specific adjustments
    SECTOR_CACHE = {} # Cache for sector information to avoid repeated API calls

    # Valuation Metrics Configuration (yfinance)
    ENABLE_VALUATION_METRICS = True # Set to False to disable yfinance valuation metrics
    YFINANCE_TIMEOUT = 60 # Timeout for yfinance requests (seconds)
    YFINANCE_RETRIES = 3 # Retries for yfinance calls
    VALUATION_CACHE = {} # Cache for valuation data to avoid repeated API calls

    # Prompt/Validation/Context Config
    PROMPT_TRACING = False
    VALUATION_TTL_SECONDS = 1800  # 30 minutes freshness window for valuation metrics
    YFINANCE_PREFLIGHT_MAX_WAIT = 180  # seconds to wait pre-flight for valuation data
    YFINANCE_PREFLIGHT_RETRY_DELAY = 5  # seconds between pre-flight attempts
    USE_TWO_PHASE_LLM = True  # Phase 2: JSON → Text two-phase generation


    # FRED Configuration
    FRED_API_KEY = os.getenv("FRED_API_KEY")
    FRED_BASE_URL = "https://api.stlouisfed.org/fred"
    FRED_SERIES_INFO = {
        "T10Y3M": "10-Year Treasury Constant Maturity Minus 3-Month Treasury Constant Maturity", # Daily
        "SP500": "S&P 500", # Daily
        "ICSA": "Initial Claims", # Weekly
        "HOUST": "New Privately-Owned Housing Units Started: Total Units", # Monthly
        "AMTMNO": "Manufacturers' New Orders: Total Manufacturing", # Monthly
        "UMCSENT": "University of Michigan: Consumer Sentiment", # Monthly
        "PPIACO": "Producer Price Index by Commodity: All Commodities", # Monthly
        "PCETRIM12M159SFRBDAL": "Trimmed Mean PCE Inflation Rate", # Monthly
        "DTWEXAFEGS": "Nominal Advanced Foreign Economies U.S. Dollar Index", # Daily
    }
    FRED_REQUEST_DELAY = 1 # Delay for FRED API calls (can be shorter than Polygon's)

# --- HTTP Session Setup ---
def setup_http_session():
    session = requests.Session()
    retries = Retry(total=Config.MAX_RETRIES, backoff_factor=1, status_forcelist=[429, 500, 502, 503, 504])
    adapter = HTTPAdapter(max_retries=retries)
    session.mount('https://', adapter)
    session.mount('http://', adapter)
    return session

# --- Prompt Builder & Utilities (Parlant-inspired) ---
class PromptBuilder:
    """
    Enhanced prompt builder inspired by Parlant's section-based architecture.

    Provides structured methods for organizing prompts into logical sections with
    clear separation of concerns. Supports context tracking, data presentation,
    and instruction formatting for improved LLM interaction reliability.
    """

    def __init__(self, on_build=None, context_name=None):
        """
        Initialize prompt builder with optional callback and context tracking.

        Args:
            on_build: Optional callback function called when build() is invoked
            context_name: Optional name for this prompt context (e.g., "stock_analysis")
        """
        self.sections = []
        self.on_build = on_build
        self.context_name = context_name or "prompt"
        self.section_metadata = {}  # Track section types for validation

    def add(self, title: str, body: str, section_type: str = "general"):
        """
        Add a section to the prompt with optional type metadata.

        Args:
            title: Section title/heading
            body: Section content
            section_type: Type of section (e.g., "context", "data", "instructions", "general")

        Returns:
            self for method chaining
        """
        self.sections.append((title, body))
        self.section_metadata[title] = section_type
        return self

    def add_context(self, title: str, content: str):
        """
        Add a context section (e.g., company profile, agent identity).

        Args:
            title: Context section title
            content: Context content

        Returns:
            self for method chaining
        """
        return self.add(title, content, section_type="context")

    def add_data_section(self, title: str, data: str, purpose: str = None):
        """
        Add a data section with optional purpose statement.

        Args:
            title: Data section title
            data: Formatted data content
            purpose: Optional purpose statement explaining the data's role

        Returns:
            self for method chaining
        """
        if purpose:
            content = f"Purpose: {purpose}\n\n{data}"
        else:
            content = data
        return self.add(title, content, section_type="data")

    def add_instructions(self, title: str, instructions: str):
        """
        Add an instructions section for LLM guidance.

        Args:
            title: Instructions section title
            instructions: Instruction content

        Returns:
            self for method chaining
        """
        return self.add(title, instructions, section_type="instructions")

    def build(self) -> str:
        """
        Build the final prompt string from all sections.

        Returns:
            Complete formatted prompt string
        """
        # Join sections with lightweight headings for traceability; do not alter semantics
        built = "\n\n".join([f"## {t}\n{b}" for t, b in self.sections])

        # Invoke callback if configured and tracing is enabled
        if self.on_build and Config.PROMPT_TRACING:
            try:
                self.on_build(self.sections, built)
            except Exception:
                pass

        return built

    def get_section_count(self) -> int:
        """Get the number of sections in this prompt."""
        return len(self.sections)

    def get_section_types(self) -> dict:
        """Get a summary of section types in this prompt."""
        type_counts = {}
        for section_type in self.section_metadata.values():
            type_counts[section_type] = type_counts.get(section_type, 0) + 1
        return type_counts


def log_prompt_sections(sections, built_text):
    """
    Trace prompt composition for debugging (Parlant-inspired logging pattern).

    Args:
        sections: List of (title, body) tuples
        built_text: Final built prompt string
    """
    try:
        section_titles = [t for t, _ in sections]
        print(f"Debug: Prompt composed with {len(sections)} section(s): {section_titles}")
        if Config.DETAILED_LOGGING:
            total_chars = len(built_text)
            print(f"Debug: Total prompt length: {total_chars} characters (~{total_chars // 4} tokens)")
    except Exception:
        pass


def format_financial_data(data: dict, indent: int = 2) -> str:
    """
    Format financial data for LLM consumption with standardized structure.

    Inspired by Parlant's event adaptation pattern for consistent data presentation.

    Args:
        data: Financial data dictionary
        indent: JSON indentation level

    Returns:
        Formatted JSON string or "N/A" if data is empty
    """
    if not data:
        return "N/A"
    try:
        return json.dumps(data, indent=indent, default=str)
    except Exception as e:
        if Config.DETAILED_LOGGING:
            print(f"Warning: Failed to format financial data: {e}")
        return "N/A"


def format_valuation_data(valuation_data: dict) -> str:
    """
    Format valuation metrics for LLM analysis with appropriate precision.

    Args:
        valuation_data: Dictionary of valuation metrics from yfinance

    Returns:
        Formatted JSON string with human-readable numbers
    """
    if not valuation_data:
        return "No valuation metrics available"

    try:
        formatted_valuation = {}
        for key, value in valuation_data.items():
            if value is not None:
                if key in ['market_cap', 'enterprise_value']:
                    # Format large numbers with commas
                    formatted_valuation[key] = f"{value:,.0f}" if isinstance(value, (int, float)) else value
                elif key in ['pe_ratio', 'forward_pe', 'pb_ratio', 'ps_ratio', 'ev_ebitda', 'ev_revenue', 'beta']:
                    # Format ratios with 2 decimal places
                    formatted_valuation[key] = f"{value:.2f}" if isinstance(value, (int, float)) else value
                else:
                    formatted_valuation[key] = value

        return json.dumps(formatted_valuation, indent=2, default=str) if formatted_valuation else "No valuation metrics available"
    except Exception as e:
        if Config.DETAILED_LOGGING:
            print(f"Warning: Failed to format valuation data: {e}")
        return "No valuation metrics available"


def format_economic_data(fred_data: dict) -> str:
    """
    Format FRED economic data for LLM analysis with appropriate precision.

    Args:
        fred_data: Dictionary of FRED economic indicators

    Returns:
        Formatted string with economic indicators
    """
    if not fred_data:
        return "No FRED economic data available."

    try:
        fred_parts = ["Recent U.S. Economic Indicators (Source: FRED, full 1-year dataset):"]
        for name, obs_list in fred_data.items():
            # Format values with appropriate decimal places based on magnitude
            formatted_obs = []
            for obs in obs_list:
                value = obs['value']
                if abs(value) < 0.1:
                    formatted_value = f"{value:.4f}"
                elif abs(value) < 10:
                    formatted_value = f"{value:.2f}"
                else:
                    formatted_value = f"{value:.1f}"
                formatted_obs.append(f"  - {obs['date']}: {formatted_value}")

            fred_parts.append(f"- {name}:\n" + "\n".join(formatted_obs))

        return "\n".join(fred_parts)
    except Exception as e:
        if Config.DETAILED_LOGGING:
            print(f"Warning: Failed to format economic data: {e}")
        return "No FRED economic data available."


class JourneyTracker:
    """
    Track processing steps for a specific analysis journey.

    Inspired by Parlant's journey system for multi-step workflow tracking.
    """

    def __init__(self, name: str, subject: str):
        """
        Initialize journey tracker.

        Args:
            name: Journey name (e.g., "stock_analysis")
            subject: Subject being analyzed (e.g., ticker symbol)
        """
        self.name = name
        self.subject = subject
        self.steps = []

    def step(self, step_name: str):
        """
        Record a step in the journey.

        Args:
            step_name: Name of the step being executed
        """
        ts = datetime.now().strftime('%H:%M:%S')
        self.steps.append((step_name, ts))
        if Config.DETAILED_LOGGING:
            print(f"Journey[{self.name}:{self.subject}] -> {step_name} @ {ts}")


class OptimizationPolicy:
    """
    Temperature progression strategy for LLM retry logic.

    Inspired by Parlant's OptimizationPolicy for robust LLM generation.
    Provides temperature values that increase with each retry attempt to
    encourage different outputs when initial attempts fail.
    """

    @staticmethod
    def get_temperature(attempt: int, max_attempts: int, strategy: str = "conservative") -> float:
        """
        Get temperature value for a given retry attempt.

        Args:
            attempt: Current attempt number (0-indexed)
            max_attempts: Maximum number of attempts
            strategy: Temperature progression strategy ("conservative", "moderate", "aggressive")

        Returns:
            Temperature value between 0.0 and 1.0
        """
        if strategy == "conservative":
            # Start very low, increase gradually (good for structured output)
            temps = [0.1, 0.2, 0.3, 0.4]
        elif strategy == "moderate":
            # Balanced progression (default for most tasks)
            temps = [0.1, 0.3, 0.6, 0.8]
        elif strategy == "aggressive":
            # Increase quickly to encourage diversity
            temps = [0.2, 0.5, 0.8, 1.0]
        else:
            # Fallback to moderate
            temps = [0.1, 0.3, 0.6, 0.8]

        # Return temperature for current attempt, capping at last value
        return temps[min(attempt, len(temps) - 1)]

    @staticmethod
    def get_temperature_ladder(max_attempts: int, strategy: str = "conservative") -> list:
        """
        Get complete temperature ladder for all attempts.

        Args:
            max_attempts: Maximum number of attempts
            strategy: Temperature progression strategy

        Returns:
            List of temperature values
        """
        return [OptimizationPolicy.get_temperature(i, max_attempts, strategy)
                for i in range(max_attempts)]


def validate_llm_text(text: str, strict: bool = True) -> tuple:
    """
    Validate LLM output format and content.

    Enhanced validation inspired by Parlant's comprehensive validation patterns.
    Ensures the LLM response contains all required elements in the correct format.

    Args:
        text: LLM response text to validate
        strict: If True, enforce all validation rules; if False, allow some flexibility

    Returns:
        Tuple of (valid: bool, reason: Optional[str])
    """
    if not text or not isinstance(text, str):
        return False, "Empty or invalid text"

    try:
        # Check 1: Exactly 6 'Buffett Score:' occurrences (one per factor)
        count_scores = len(re.findall(r"Buffett Score:", text))
        if count_scores != 6:
            return False, f"Expected 6 'Buffett Score:' occurrences, found {count_scores}"

        # Check 2: Valid llm_qualitative_score line with correct format
        score_line = re.search(r"llm_qualitative_score:\s*([-+]?\d+(?:\.\d+)?)\s*/\s*100", text)
        if not score_line:
            return False, "Missing or malformed 'llm_qualitative_score: X/100' line"

        # Check 3: Score value is within valid range
        try:
            score = float(score_line.group(1))
            if not (-100 <= score <= 100):
                return False, f"llm_qualitative_score {score} out of valid range [-100, 100]"
        except ValueError:
            return False, "Could not parse llm_qualitative_score as number"

        # Check 4: Extract individual factor scores and validate ranges
        if strict:
            factor_scores = re.findall(r"Buffett Score:\s*([-+]?\d+)", text)
            if len(factor_scores) != 6:
                return False, f"Could not extract 6 factor scores, found {len(factor_scores)}"

            for i, score_str in enumerate(factor_scores, 1):
                try:
                    factor_score = int(score_str)
                    if not (-100 <= factor_score <= 100):
                        return False, f"Factor {i} score {factor_score} out of valid range [-100, 100]"
                except ValueError:
                    return False, f"Factor {i} score '{score_str}' is not a valid integer"

        # Check 5: Verify all required factor names are present
        if strict:
            required_factors = [
                "SUSTAINABLE COMPETITIVE ADVANTAGE",
                "INNOVATION CAPACITY",
                "CORPORATE GOVERNANCE",
                "FINANCIAL REPORTING QUALITY",
                "COMPETITIVE POSITION",
                "WONDERFUL BUSINESSES AT FAIR PRICES"
            ]
            for factor in required_factors:
                if factor not in text:
                    return False, f"Missing required factor: {factor}"

        # All checks passed
        return True, None

    except Exception as e:
        return False, f"Validation exception: {e}"


def ensure_valuation_metrics_fresh(ticker_symbol: str):
    """
    Pre-flight: keep retrying yfinance valuation fetch until success or until MAX_WAIT is exceeded.
    Returns valuation data dict or None if timed out.
    """
    start = time.time()
    while True:
        data = fetch_valuation_metrics(ticker_symbol)
        if data:
            return data
        elapsed = time.time() - start
        if elapsed >= getattr(Config, 'YFINANCE_PREFLIGHT_MAX_WAIT', 180):
            print(f"Warning [{ticker_symbol}]: Pre-flight valuation fetch timed out after {int(elapsed)}s.")
            return None
        delay = getattr(Config, 'YFINANCE_PREFLIGHT_RETRY_DELAY', 5)
        print(f"Info [{ticker_symbol}]: Retrying valuation fetch in {delay}s (elapsed {int(elapsed)}s)...")
        time.sleep(delay)



# --- Rate-Limited API Request ---
def rate_limited_request(session, method, url, delay_override=None, **kwargs):
    actual_delay = Config.REQUEST_DELAY # Default to Polygon delay
    api_key_in_params = False

    if 'polygon.io' in url:
        if Config.POLYGON_API_KEY and 'apiKey' not in url: # Check if API key is already in URL string
            # Add apiKey to params if not in URL and key exists
            params = kwargs.get('params', {})
            if 'apiKey' not in params:
                params['apiKey'] = Config.POLYGON_API_KEY
                kwargs['params'] = params
            api_key_in_params = 'apiKey' in params
    elif 'api.stlouisfed.org' in url:
        actual_delay = Config.FRED_REQUEST_DELAY # Use FRED specific delay
        # FRED API key is expected to be in params by the calling function
        api_key_in_params = 'api_key' in kwargs.get('params', {})


    if delay_override is not None:
        actual_delay = delay_override

    if Config.DETAILED_LOGGING: print(f"Waiting {actual_delay}s before request to {url}...")
    time.sleep(actual_delay)

    if Config.DETAILED_LOGGING: print(f"Making request: {method} {url} with params {kwargs.get('params')}")
    response = session.request(method, url, **kwargs)

    if response.status_code == 429:
        print(f"WARNING: Rate limit hit (429) for {url}. Consider increasing request delay for this service.")
    response.raise_for_status() # Raises HTTPError for bad responses (4XX or 5XX)
    return response

# --- Helper: Get Financial Value ---
def get_financial_value(period_financials, statement_key, field_keys, default=None):
    if not period_financials or statement_key not in period_financials: return default
    statement_data = period_financials.get(statement_key)
    if not statement_data: return default
    for key in field_keys:
        item_data = statement_data.get(key)
        if item_data and isinstance(item_data, dict) and 'value' in item_data and item_data['value'] is not None:
            value = item_data['value']
            if isinstance(value, str) and (value.strip() == "" or value.isalpha()):
                if Config.DETAILED_LOGGING: print(f"Debug: Rejected non-numeric string value '{value}' for key '{key}'")
                continue
            return value
    return default

# --- Helper: Safe Division ---
def safe_divide(numerator, denominator, default=None):
    if numerator is None or denominator is None: return default
    try:
        num, den = float(numerator), float(denominator)
    except (ValueError, TypeError):
        if Config.DETAILED_LOGGING: print(f"Debug: Safe divide failed type/value conversion for {numerator} / {denominator}")
        return default
    return (num / den) if den != 0 else default

# --- Fetch Valuation Metrics using yfinance ---
def fetch_valuation_metrics(ticker_symbol):
    """
    Fetch valuation metrics using yfinance API for LLM analysis with retry logic and freshness TTL.
    Returns a dictionary with valuation metrics or None if failed after all retries.
    """
    if not Config.ENABLE_VALUATION_METRICS:
        if Config.DETAILED_LOGGING:
            print(f"Debug [{ticker_symbol}]: Valuation metrics disabled in config")
        return None

    # Serve fresh cache first
    entry = Config.VALUATION_CACHE.get(ticker_symbol)
    if isinstance(entry, dict) and 'data' in entry and 'ts' in entry:
        age = time.time() - entry['ts']
        if age < getattr(Config, 'VALUATION_TTL_SECONDS', 1800):
            if Config.DETAILED_LOGGING:
                print(f"Debug [{ticker_symbol}]: Using cached valuation data (age {int(age)}s)")
            return entry['data']
    elif entry is not None:
        # Back-compat: upgrade legacy cache entry to timestamped form
        Config.VALUATION_CACHE[ticker_symbol] = {'data': entry, 'ts': time.time()}
        if Config.DETAILED_LOGGING:
            print(f"Debug [{ticker_symbol}]: Upgraded legacy cache entry to timestamped form")
        return entry

    # Retry logic for yfinance API calls
    for attempt in range(Config.YFINANCE_RETRIES):
        try:
            if Config.DETAILED_LOGGING or attempt > 0:
                print(f"Info [{ticker_symbol}]: Fetching valuation metrics from yfinance (attempt {attempt+1}/{Config.YFINANCE_RETRIES})...")

            ticker = yf.Ticker(ticker_symbol)
            info = ticker.info
            if not info:
                print(f"Warning [{ticker_symbol}]: No info data available from yfinance (attempt {attempt+1})")
                if attempt < Config.YFINANCE_RETRIES - 1:
                    print(f"Info [{ticker_symbol}]: Retrying yfinance call in 5 seconds...")
                    time.sleep(5)
                    continue
                return None

            valuation_data = {
                'market_cap': info.get('marketCap'),
                'enterprise_value': info.get('enterpriseValue'),
                'pe_ratio': info.get('trailingPE'),
                'forward_pe': info.get('forwardPE'),
                'pb_ratio': info.get('priceToBook'),
                'ps_ratio': info.get('priceToSalesTrailing12Months'),
                'ev_ebitda': info.get('enterpriseToEbitda'),
                'ev_revenue': info.get('enterpriseToRevenue'),
                'beta': info.get('beta'),
                'current_price': info.get('currentPrice') or info.get('regularMarketPrice'),
                'book_value_per_share': info.get('bookValue'),
                'earnings_per_share': info.get('trailingEps'),
                'revenue_per_share': info.get('revenuePerShare')
            }

            # Cache with timestamp
            Config.VALUATION_CACHE[ticker_symbol] = {'data': valuation_data, 'ts': time.time()}

            non_none_metrics = {k: v for k, v in valuation_data.items() if v is not None}
            print(f"Info [{ticker_symbol}]: Successfully fetched {len(non_none_metrics)} valuation metrics from yfinance")
            return valuation_data

        except Exception as e:
            print(f"Error [{ticker_symbol}]: Failed to fetch valuation metrics from yfinance (attempt {attempt+1}): {e}")
            if attempt < Config.YFINANCE_RETRIES - 1:
                print(f"Info [{ticker_symbol}]: Retrying yfinance call in 5 seconds...")
                time.sleep(5)

    print(f"Error [{ticker_symbol}]: Failed to fetch valuation metrics from yfinance after {Config.YFINANCE_RETRIES} attempts")
    return None

# --- Sector Detection and Mapping ---
def get_company_sector(ticker, session=None):
    """
    Get sector information for a company ticker using Polygon.io API.
    Returns standardized sector classification for quality metric adjustments.
    """
    if not Config.ENABLE_SECTOR_ADJUSTMENTS:
        return 'general'

    # Check cache first
    if ticker in Config.SECTOR_CACHE:
        return Config.SECTOR_CACHE[ticker]

    sector = 'general'  # Default sector
    try:
        if Config.POLYGON_API_KEY:
            if session is None:
                session = setup_http_session()

            # Use ticker details endpoint to get sector information
            url = f"{Config.POLYGON_BASE_URL}/v3/reference/tickers/{ticker}"
            resp = rate_limited_request(session, "GET", url)
            data = resp.json()

            if 'results' in data and data['results']:
                results = data['results']

                # Try to get sector from various fields
                raw_sector = None
                if 'sic_description' in results:
                    raw_sector = results['sic_description']
                elif 'sector' in results:
                    raw_sector = results['sector']
                elif 'industry' in results:
                    raw_sector = results['industry']

                if raw_sector:
                    sector = map_to_standard_sector(raw_sector, ticker)
                    if Config.DETAILED_LOGGING:
                        print(f"Debug [{ticker}]: Raw sector '{raw_sector}' mapped to '{sector}'")

    except Exception as e:
        if Config.DETAILED_LOGGING:
            print(f"Warning [{ticker}]: Could not fetch sector information: {e}")

    # Cache the result
    Config.SECTOR_CACHE[ticker] = sector
    return sector

def map_to_standard_sector(raw_sector, ticker):
    """
    Map raw sector/industry descriptions to standardized sectors for quality adjustments.
    """
    if not raw_sector:
        return 'general'

    raw_lower = raw_sector.lower()

    # REIT Detection
    if any(keyword in raw_lower for keyword in [
        'real estate', 'reit', 'property', 'realty', 'land', 'mortgage'
    ]):
        return 'reit'

    # Utilities Detection
    if any(keyword in raw_lower for keyword in [
        'electric', 'utility', 'utilities', 'power', 'gas', 'water', 'energy distribution'
    ]):
        return 'utility'

    # Healthcare/Biotech Detection (check before technology to catch biotechnology)
    if any(keyword in raw_lower for keyword in [
        'healthcare', 'pharmaceutical', 'biotech', 'medical', 'drug', 'health',
        'biotechnology', 'life sciences', 'therapeutics'
    ]):
        return 'healthcare'

    # Technology Detection
    if any(keyword in raw_lower for keyword in [
        'technology', 'software', 'computer', 'internet', 'semiconductor', 'tech',
        "information technology", 'telecommunications', 'electronic', 'digital'
    ]):
        return 'technology'

    # Financial Services Detection
    if any(keyword in raw_lower for keyword in [
        'bank', 'financial', 'insurance', 'investment', 'credit', 'finance',
        'securities', 'asset management', 'lending'
    ]):
        return 'financial'

    # Industrial Detection
    if any(keyword in raw_lower for keyword in [
        'industrial', 'manufacturing', 'machinery', 'aerospace', 'defense',
        'construction', 'materials', 'chemicals'
    ]):
        return 'industrial'

    return 'general'

# --- Sector-Specific Adjustment Functions ---
def apply_sector_adjustments_profitability(metrics, sector, ticker):
    """
    Apply sector-specific adjustments to profitability metrics.
    """
    adjusted_metrics = metrics.copy()

    if sector == 'reit':
        # REITs typically have lower ROE due to large asset/equity base
        # Average ROE for U.S. equity REITs is ~4.7%
        roe = adjusted_metrics.get('roe')
        if roe is not None:
            # Adjust ROE expectations: treat 5% as normal (no penalty)
            if Config.DETAILED_LOGGING:
                print(f"Debug [{ticker}]: REIT ROE adjustment - Original: {roe:.4f}")

    elif sector == 'utility':
        # Regulated utilities have authorized ROEs around 9.5-10%
        roe = adjusted_metrics.get('roe')
        if roe is not None:
            # Treat ROE ~9-10% as normal for utilities
            if Config.DETAILED_LOGGING:
                print(f"Debug [{ticker}]: Utility ROE adjustment - Original: {roe:.4f}")

    elif sector == 'technology':
        # Tech firms are R&D-intensive; consider R&D-adjusted metrics
        # Capitalizing R&D dramatically raises ROE
        if Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: Technology sector - Consider R&D intensity in profitability")

    elif sector == 'healthcare':
        # Biotech/pharma have heavy R&D spend
        # Allow negative ROE due to R&D investments
        if Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: Healthcare sector - R&D-intensive, allow negative ROE")

    return adjusted_metrics

def apply_sector_adjustments_payout(metrics, sector, ticker):
    """
    Apply sector-specific adjustments to payout efficiency metrics.
    """
    adjusted_metrics = metrics.copy()

    if sector == 'reit':
        # REITs required to distribute ≥90% of taxable income
        # High payout ratios (≥90%) are normal and required by law
        dividend_payout = adjusted_metrics.get('dividend_payout')
        if dividend_payout is not None and Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: REIT payout adjustment - High payouts (≥90%) are normal")

    elif sector == 'utility':
        # Utilities historically payout ~60-65% of earnings
        # Allow high payouts (~60-70%) without penalty
        dividend_payout = adjusted_metrics.get('dividend_payout')
        if dividend_payout is not None and Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: Utility payout adjustment - High payouts (60-70%) are normal")

    return adjusted_metrics

def apply_sector_adjustments_investment(metrics, sector, ticker):
    """
    Apply sector-specific adjustments to investment quality metrics.
    """
    adjusted_metrics = metrics.copy()

    if sector == 'technology' or sector == 'healthcare':
        # High-growth sectors often require high reinvestment
        # Allow high investment (capex/R&D) rates
        asset_growth = adjusted_metrics.get('asset_growth')
        investing_flow_efficiency = adjusted_metrics.get('investing_flow_efficiency')
        if Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: {sector.title()} sector - Allow high investment rates")

    elif sector == 'industrial' or sector == 'utility':
        # Capital-intensive industries have low asset turnover
        # Expect low turnover (high asset base); no penalty
        asset_turnover = adjusted_metrics.get('asset_turnover')
        if asset_turnover is not None and Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: {sector.title()} sector - Capital-intensive, expect low asset turnover")

    return adjusted_metrics

def apply_sector_adjustments_accounting(metrics, sector, ticker):
    """
    Apply sector-specific adjustments to accounting quality metrics.
    """
    adjusted_metrics = metrics.copy()

    if sector == 'financial':
        # Financial firms inherently use leverage
        # Emphasize cash-flow/debt or coverage, not raw D/E ratios
        if Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: Financial sector - Focus on cash flow metrics over leverage ratios")

    return adjusted_metrics

# --- Fetch Dividends ---
def fetch_period_dividends_per_share(session, company, start_date, end_date):
    if not start_date or not end_date:
        print(f"Warning [{company}]: Cannot fetch dividends without period start/end dates.")
        return None

    base_dividends_url = f"{Config.POLYGON_BASE_URL}/v3/reference/dividends"
    params = {"ticker": company, "ex_dividend_date.gte": start_date, "ex_dividend_date.lte": end_date, "limit": 100, "dividend_type": "CD"}
    total_dividend_per_share = 0.0
    next_url = base_dividends_url
    page_count = 0

    try:
        if Config.DETAILED_LOGGING: print(f"Debug [{company}]: Fetching dividends between {start_date} and {end_date}...")
        while next_url:
            current_params = params.copy() if page_count == 0 else {} # Use full params only for the first request
            request_url = next_url

            # For paginated requests, Polygon's next_url might not include apiKey.
            # rate_limited_request handles adding apiKey to params if not in URL.
            # If apiKey is already in next_url, params should not also have it.
            if page_count > 0 and Config.POLYGON_API_KEY and 'apiKey=' in request_url and 'apiKey' in current_params:
                del current_params['apiKey']

            resp = rate_limited_request(session, "GET", request_url, params=current_params)
            data = resp.json()
            results = data.get('results', [])
            page_count += 1

            if not results and page_count == 1:
                if Config.DETAILED_LOGGING: print(f"Debug [{company}]: No cash dividends found in the specified period.")
                return 0.0

            for dividend in results:
                ex_date, cash_amount, div_type = dividend.get("ex_dividend_date"), dividend.get("cash_amount"), dividend.get("dividend_type")
                if ex_date and cash_amount is not None and div_type == 'CD':
                    try:
                        if start_date <= parse(ex_date).strftime('%Y-%m-%d') <= end_date:
                             total_dividend_per_share += float(cash_amount)
                    except (ValueError, TypeError):
                        print(f"Warning [{company}]: Could not parse dividend date '{ex_date}' or amount '{cash_amount}'.")

            next_url = data.get('next_url')
            # Ensure subsequent next_url calls will have apiKey handled by rate_limited_request or is already in URL
            if next_url and Config.POLYGON_API_KEY and 'apiKey=' not in next_url:
                # If we need to add it to the URL string itself for some reason (though params is preferred)
                # next_url += ('&' if '?' in next_url else '?') + f"apiKey={Config.POLYGON_API_KEY}"
                pass # rate_limited_request will handle adding it to params

        print(f"Info [{company}]: Fetched dividends across {page_count} page(s). Total DPS: {total_dividend_per_share:.4f}")
        return total_dividend_per_share
    except requests.exceptions.HTTPError as http_err:
         if http_err.response.status_code == 429: print(f"Warning [{company}]: Rate limit hit (429) fetching dividends.")
         else: print(f"Error [{company}]: HTTP error fetching dividends: {http_err}")
    except Exception as e: print(f"Error [{company}]: Exception fetching/processing dividends: {e}")
    return None

# --- Calculate Quality Metrics ---
def validate_financials(fin_data, ticker):
    """Check for data consistency and quality"""
    if not fin_data: return False

    # Check for balance sheet equation: Assets = Liabilities + Equity
    assets = get_financial_value(fin_data, 'balance_sheet', ['assets'])
    liabilities = get_financial_value(fin_data, 'balance_sheet', ['liabilities'])
    equity = get_financial_value(fin_data, 'balance_sheet', ['equity_attributable_to_parent', 'equity'])

    # If all values present, check balance
    if assets is not None and liabilities is not None and equity is not None:
        try:
            a, l, e = float(assets), float(liabilities), float(equity)
            # Allow for some rounding differences (within 5%)
            if abs((l + e) - a) / a > 0.05:
                print(f"Warning [{ticker}]: Balance sheet equation off by more than 5%")
                # Don't reject data, just warn
        except (ValueError, TypeError): pass

    # Check consistency of cash flow statements
    net_cash_op = get_financial_value(fin_data, 'cash_flow_statement',
        ['net_cash_flow_from_operating_activities_continuing', 'net_cash_flow_from_operating_activities'])
    net_cash_inv = get_financial_value(fin_data, 'cash_flow_statement',
        ['net_cash_flow_from_investing_activities_continuing', 'net_cash_flow_from_investing_activities'])
    net_cash_fin = get_financial_value(fin_data, 'cash_flow_statement',
        ['net_cash_flow_from_financing_activities_continuing', 'net_cash_flow_from_financing_activities'])

    # If all values present, check total cash flow
    if net_cash_op is not None and net_cash_inv is not None and net_cash_fin is not None:
        try:
            total_cf = float(net_cash_op) + float(net_cash_inv) + float(net_cash_fin)
            net_change_cash = get_financial_value(fin_data, 'cash_flow_statement', ['net_change_in_cash'])
            if net_change_cash is not None:
                if abs(total_cf - float(net_change_cash)) / abs(float(net_change_cash)) > 0.1:
                    print(f"Warning [{ticker}]: Cash flow components don't sum to net change in cash (>10% difference)")
        except (ValueError, TypeError): pass

    return True  # Still use data even if validation issues

def calculate_quality_metrics(financial_data, session):
    # Initialize key variables that might be referenced before definition
    ebitda = None
    company_ticker = financial_data.get('company', 'UNKNOWN')
    raw_financials = financial_data.get('financials', [])
    if not raw_financials or len(raw_financials) < 2:
        print(f"Error [{company_ticker}]: Insufficient financial periods (< 2) from /vX endpoint."); return None
    if not all(isinstance(p.get('financials'), dict) for p in raw_financials[:2]):
        print(f"Error [{company_ticker}]: 'financials' dictionary missing or invalid in period data."); return None

    # Get sector information for adjustments
    company_sector = get_company_sector(company_ticker, session)
    if Config.DETAILED_LOGGING:
        print(f"Debug [{company_ticker}]: Detected sector: {company_sector}")

    # Validate financial data quality
    latest_fin = raw_financials[0]['financials']
    if not validate_financials(latest_fin, company_ticker):
        print(f"Warning [{company_ticker}]: Financial data quality validation issues detected.")

    latest_p, prior_p = raw_financials[0], raw_financials[1]
    latest_fin, prior_fin = latest_p['financials'], prior_p['financials']
    start_date, end_date = latest_p.get("start_date"), latest_p.get("end_date")

    # Income Statement
    revenue = get_financial_value(latest_fin, 'income_statement', ['revenues'])
    net_income = get_financial_value(latest_fin, 'income_statement', ['net_income_loss_attributable_to_parent', 'net_income_loss'])
    op_income = get_financial_value(latest_fin, 'income_statement', ['operating_income_loss'])
    gross_profit = get_financial_value(latest_fin, 'income_statement', ['gross_profit'])
    interest_exp = get_financial_value(latest_fin, 'income_statement', ['interest_expense_operating'])
    shares_basic = get_financial_value(latest_fin, 'income_statement', ['basic_average_shares', 'weighted_average_shares_outstanding_basic'])
    shares_basic_prev = get_financial_value(prior_fin, 'income_statement', ['basic_average_shares', 'weighted_average_shares_outstanding_basic'])

    # Balance Sheet
    assets = get_financial_value(latest_fin, 'balance_sheet', ['assets'])
    liabilities = get_financial_value(latest_fin, 'balance_sheet', ['liabilities'])
    equity = get_financial_value(latest_fin, 'balance_sheet', ['equity_attributable_to_parent', 'equity'])
    assets_prev = get_financial_value(prior_fin, 'balance_sheet', ['assets'])

    # Additional Balance Sheet Items
    current_assets = get_financial_value(latest_fin, 'balance_sheet', ['current_assets'])
    current_liabilities = get_financial_value(latest_fin, 'balance_sheet', ['current_liabilities'])

    # Cash Flow Statement
    net_cash_op = get_financial_value(latest_fin, 'cash_flow_statement', ['net_cash_flow_from_operating_activities_continuing', 'net_cash_flow_from_operating_activities'])
    net_cash_inv = get_financial_value(latest_fin, 'cash_flow_statement', ['net_cash_flow_from_investing_activities_continuing', 'net_cash_flow_from_investing_activities'])



    # Add back the equity calculation from assets and liabilities if missing
    if equity is None and assets is not None and liabilities is not None:
        try:
            equity = float(assets) - float(liabilities)
            if Config.DETAILED_LOGGING:
                print(f"Debug [{company_ticker}]: Calculated equity from assets and liabilities: {equity}")
        except (ValueError, TypeError):
            equity = None

    # EBITDA calculation - define this variable early
    ebitda = get_financial_value(latest_fin, 'income_statement', ['ebitda'])

    # Safely convert net_cash_inv to float for capex_proxy

    # If EBITDA not directly available, calculate from operating income
    if ebitda is None and op_income is not None:
        depreciation_amort = get_financial_value(latest_fin, 'income_statement',
            ['depreciation_and_amortization', 'depreciation', 'amortization'])
        if depreciation_amort is not None:
            try:
                ebitda = float(op_income) + float(depreciation_amort)
            except (ValueError, TypeError):
                if Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Could not calculate EBITDA")

    # Better CAPEX tracking
    capex = get_financial_value(latest_fin, 'cash_flow_statement',
        ['capital_expenditure', 'payments_to_acquire_property_plant_equipment'])

    # Fall back to proxy if direct value not available
    capex_proxy = None
    if net_cash_inv is not None:
        try:
            capex_proxy = abs(float(net_cash_inv))
        except (ValueError, TypeError):
            if Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Could not convert net_cash_inv to float for capex_proxy")

    # Use direct capex if available, otherwise use proxy
    if capex is None:
        capex = capex_proxy

    dividends = None
    if company_ticker and start_date and end_date:
        dps = fetch_period_dividends_per_share(session, company_ticker, start_date, end_date)
        if dps is not None and shares_basic is not None:
            try:
                shares_num = float(shares_basic)
                if shares_num > 0: dividends = dps * shares_num
                elif dps == 0.0: dividends = 0.0
            except (ValueError, TypeError):
                if Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Could not convert shares_basic '{shares_basic}' to float for dividend calculation")
                # Keep dividends as None
        elif dps is None and Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Dividend fetch failed for total dividend calculation.")
        elif shares_basic is None and Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Shares Basic missing for total dividend calculation.")


    # Metric Calculations
    roe = safe_divide(net_income, equity)
    roa = safe_divide(net_income, assets)
    op_margin = safe_divide(op_income, revenue)
    gross_margin = safe_divide(gross_profit, revenue)



    # Working capital
    working_capital = None
    if current_assets is not None and current_liabilities is not None:
        try:
            working_capital = float(current_assets) - float(current_liabilities)
        except (ValueError, TypeError):
            if Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Could not calculate working capital")

    # Free cash flow
    fcf = None
    if net_cash_op is not None and capex is not None:
        try:
            fcf = float(net_cash_op) - float(capex)
        except (ValueError, TypeError):
            if Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Could not calculate free cash flow")

    # FCF to revenue ratio (no market cap in basic data)
    fcf_to_revenue = safe_divide(fcf, revenue)

    accruals_ratio = None
    if net_income is not None and net_cash_op is not None and assets is not None and assets_prev is not None:
        try:
            avg_assets = (float(assets) + float(assets_prev)) / 2
            accruals_ratio = safe_divide(float(net_income) - float(net_cash_op), avg_assets)
        except (ValueError, TypeError): pass

    earnings_quality = safe_divide(net_cash_op, net_income)
    div_payout = None
    if dividends is not None and net_income is not None:
        try:
            ni_num = float(net_income)
            if ni_num > 0: div_payout = safe_divide(dividends, ni_num)
        except (ValueError, TypeError): pass

    share_dilution = None
    if shares_basic is not None and shares_basic_prev is not None:
        try: share_dilution = safe_divide(float(shares_basic) - float(shares_basic_prev), float(shares_basic_prev))
        except (ValueError, TypeError): pass

    asset_growth = None
    if assets is not None and assets_prev is not None:
        try: asset_growth = safe_divide(float(assets) - float(assets_prev), float(assets_prev))
        except (ValueError, TypeError): pass

    inv_flow_eff = safe_divide(capex_proxy, revenue)
    inv_flow_rate = safe_divide(capex_proxy, net_cash_op)

    asset_turnover = None
    if revenue is not None and assets is not None and assets_prev is not None:
        try:
            avg_assets = (float(assets) + float(assets_prev)) / 2
            asset_turnover = safe_divide(revenue, avg_assets)
        except (ValueError, TypeError): pass



    # FCF to dividend ratio if both available
    fcf_to_dividend = None
    if fcf is not None and dividends is not None and dividends > 0:
        try:
            fcf_to_dividend = float(fcf) / float(dividends)
        except (ValueError, TypeError):
            pass

    # Create base metrics
    base_metrics = {
        "profitability": {"roe": roe, "roa": roa, "operating_margin": op_margin, "gross_margin": gross_margin},
        "accounting_quality": {"accruals_ratio": accruals_ratio, "earnings_quality": earnings_quality},
        "payout_efficiency": {"dividend_payout": div_payout, "share_dilution": share_dilution, "fcf_to_dividend": fcf_to_dividend},
        "investment_quality": {"asset_growth": asset_growth, "investing_flow_efficiency": inv_flow_eff, "investing_flow_rate": inv_flow_rate, "asset_turnover": asset_turnover, "fcf_to_revenue": fcf_to_revenue},
        "raw_values_latest": {
            "filing_date": latest_p.get("filing_date"), "period_end_date": end_date, "revenue": revenue, "net_income": net_income, "operating_income": op_income,
            "gross_profit": gross_profit, "total_assets": assets, "total_assets_prev": assets_prev, "equity": equity, "total_liabilities": liabilities,
            "interest_expense": interest_exp, "net_cash_op": net_cash_op, "net_cash_investing": net_cash_inv, "capex": capex, "capex_proxy": capex_proxy,
            "dividends": dividends, "shares_basic": shares_basic, "current_assets": current_assets, "current_liabilities": current_liabilities,
            "ebitda": ebitda, "working_capital": working_capital, "free_cash_flow": fcf
        }
    }

    # Apply sector-specific adjustments
    if Config.ENABLE_SECTOR_ADJUSTMENTS:
        base_metrics["profitability"] = apply_sector_adjustments_profitability(base_metrics["profitability"], company_sector, company_ticker)
        base_metrics["accounting_quality"] = apply_sector_adjustments_accounting(base_metrics["accounting_quality"], company_sector, company_ticker)
        base_metrics["payout_efficiency"] = apply_sector_adjustments_payout(base_metrics["payout_efficiency"], company_sector, company_ticker)
        base_metrics["investment_quality"] = apply_sector_adjustments_investment(base_metrics["investment_quality"], company_sector, company_ticker)

    # Add sector information to metrics
    base_metrics["sector_info"] = {
        "sector": company_sector,
        "adjustments_enabled": Config.ENABLE_SECTOR_ADJUSTMENTS
    }

    metrics = base_metrics
    # Round metrics after calculation
    for category_metrics in metrics.values():
        if isinstance(category_metrics, dict):
            for k, v in category_metrics.items():
                if isinstance(v, (float, int)) and k not in ["shares_basic", "dividends"]: # Avoid rounding shares/dividends here
                    if k in ['earnings_quality', 'asset_turnover']:
                        category_metrics[k] = round(v, 2) if v != float('inf') else float('inf')
                    else:
                        category_metrics[k] = round(v, 4)

    metrics["base_quality_score"] = calculate_composite_quality(metrics, company_ticker)
    return metrics

# --- Scoring Functions ---
def winsorize(val, lower=-float('inf'), upper=float('inf')):
    if val is None: return None
    try:
        f_val = float(val)
        if f_val == float('inf'): return upper
        if f_val == float('-inf'): return lower
        return max(min(f_val, upper), lower)
    except (ValueError, TypeError): return None

def score_profitability(m, t, sector=None):
    s, c = 0, 0

    # Get sector information if not provided
    if sector is None:
        sector = Config.SECTOR_CACHE.get(t, 'general')

    # ROE: Research shows top decile (15-20%+) significantly outperforms. High ROE cutoff ~15-20% for max score
    # Apply sector-specific adjustments
    roe = winsorize(m.get('roe'), -0.5, 1.0)
    if roe is not None:
        if sector == 'reit':
            # REITs: Average ROE ~4.7%, treat 5% as normal (no penalty)
            s += (40 if roe >= 0.08 else 30 if roe >= 0.05 else 15 if roe >= 0.03 else -5 if roe < 0 else 0)
        elif sector == 'utility':
            # Utilities: Authorized ROEs around 9.5-10%, treat 9-10% as normal
            # Adjust thresholds to be more appropriate for utility sector
            s += (40 if roe >= 0.10 else 30 if roe >= 0.07 else 20 if roe >= 0.05 else 10 if roe >= 0.03 else -10 if roe < 0 else 0)
        elif sector == 'healthcare':
            # Healthcare/Biotech: Allow negative ROE due to R&D investments
            s += (40 if roe >= 0.15 else 30 if roe >= 0.10 else 15 if roe >= 0.05 else 5 if roe >= 0 else 0)  # No penalty for negative
        else:
            # Standard evaluation for other sectors
            s += (40 if roe >= 0.15 else 30 if roe >= 0.10 else 15 if roe >= 0.05 else -10 if roe < 0 else 0)
        c += 1

    # Operating Margin: Top quintile (~15-20%+) shows highest returns. Reward materially positive margins
    om = winsorize(m.get('operating_margin'), -0.5, 1.0)
    if om is not None:
        s += (35 if om >= 0.15 else 25 if om >= 0.10 else 10 if om >= 0.05 else -10 if om < 0 else 0)
        c += 1

    # Gross Margin: Novy-Marx (2013) shows gross profit is powerful. Very high margins (50-60%+) are excellent
    gm = winsorize(m.get('gross_margin'), 0.0, 1.0)
    if gm is not None:
        s += (25 if gm >= 0.50 else 20 if gm >= 0.40 else 10 if gm >= 0.25 else 0 if gm >= 0 else -5)
        c += 1
    else:
        s -= 5  # Penalize missing gross margin data

    final = min(max(s, 0), 100) if c > 0 else 40
    if Config.DETAILED_LOGGING:
        print(f"Debug [{t}]: Profitability Score = {final} (Count={c}, ROE={roe}, OM={om}, GM={gm}, Sector={sector})")
    return final

def score_accounting(m, t):
    b, s, c = 60, 0, 0

    # Accruals Ratio: Sloan (1996) shows low accruals outperform by ~2% annually
    # Firms with accruals <0 (cash flow > earnings) score highest, large positive accruals score poorly
    ar = winsorize(m.get('accruals_ratio'), -0.5, 0.5)
    if ar is not None:
        s += (30 if ar < 0 else 20 if ar <= 0.02 else 10 if ar <= 0.05 else -10 if ar <= 0.15 else -20)
        c += 1
    else:
        s -= 5

    # Earnings Quality (CFO/Net Income): Conservative accounting with CFO > Net Income is preferred
    # Piotroski F-score uses CFO > Net Income as quality indicator
    eq = winsorize(m.get('earnings_quality'), -1.0, 3.0)
    if eq is not None:
        s += (30 if eq >= 1.2 else 20 if eq >= 1.0 else 5 if eq >= 0.8 else -15 if eq < 0.7 else 0)
        c += 1
    else:
        s -= 5

    final = max(min(b + s, 100), 0) if c > 0 else 40
    if Config.DETAILED_LOGGING: print(f"Debug [{t}]: Accounting Score = {final} (Count={c}, AR={ar}, EQ={eq})")
    return final

def score_payout(m, t, sector=None):
    b, s, c = 60, 0, 0; dp, sd = None, None

    # Get sector information if not provided
    if sector is None:
        sector = Config.SECTOR_CACHE.get(t, 'general')

    if m and isinstance(m, dict):
        # Dividend Payout Ratio: Research shows top-decile dividend yield portfolios outperform
        # Boudoukh et al. (2007) found ~0.23% extra monthly return for high dividend yield firms
        # Moderate-to-high payout ratios (30-60% range) are optimal, very high (>70-80%) unsustainable
        dp = winsorize(m.get('dividend_payout'), 0.0, 1.5)
        if dp is not None:
            c += 1
            # Adjust scoring based on sector
            if sector == 'reit':
                # REITs required to distribute ≥90% of taxable income, so high payouts are normal
                s += 35 if 0.60 <= dp <= 0.95 else 20 if 0.40 <= dp < 0.60 else 10 if dp > 0.95 else 5
            elif sector == 'utility':
                # Utilities historically payout ~60-65% of earnings, allow high payouts without penalty
                s += 35 if 0.40 <= dp <= 0.75 else 20 if 0.20 <= dp < 0.40 else -10 if dp > 0.85 else 5
            else:
                # Standard evaluation: reward moderate-to-high payout (top quintile by yield)
                s += 35 if 0.30 <= dp <= 0.60 else 20 if 0.15 <= dp < 0.30 else 5 if dp == 0 else -15 if dp > 0.75 else 0
        else:
            s -= 5  # Metric N/A

        # Share Dilution: Research shows low net issuance (buybacks) earned ~3.6% annual excess return
        # Reward firms with little or negative net issuance (net buybacks), penalize high issuance
        sd = winsorize(m.get('share_dilution'), -0.15, 0.15)
        if sd is not None:
            c += 1
            s += (25 if sd <= -0.02 else 15 if sd <= 0 else 5 if sd <= 0.02 else -15 if sd <= 0.05 else -25)
        else:
            s -= 5  # Metric N/A

        # Check FCF coverage for dividends if available
        fcf_to_div = m.get('fcf_to_dividend')
        if fcf_to_div is not None:
            c+=1
            fcf_to_div = winsorize(fcf_to_div, 0.0, 5.0)
            s += 20 if fcf_to_div > 1.5 else 10 if fcf_to_div > 1.0 else -15 if fcf_to_div < 0.7 else 0
    else: s = -10 # Category missing

    final = max(min(b + s, 100), 0) if c > 0 else 30
    if Config.DETAILED_LOGGING:
        print(f"Debug [{t}]: Payout Score = {final} (Count={c}, DP={dp}, SD={sd}, Sector={sector})")
    return final

def score_investment(m, t, sector=None):
    b, s, c = 50, 0, 0

    # Get sector information if not provided
    if sector is None:
        sector = Config.SECTOR_CACHE.get(t, 'general')

    # Asset Growth: Research shows lowest-investment decile (slow growth) earned ~3% annual excess return
    # Low asset growth (<5-10% per year) scores high, very high growth (>20-25%) scores low
    # Apply sector-specific adjustments
    ag = winsorize(m.get('asset_growth'), -0.1, 0.5)
    if ag is not None:
        if sector in ['technology', 'healthcare']:
            # High-growth sectors: allow higher asset growth rates
            s += (25 if ag <= 0.10 else 15 if ag <= 0.20 else 5 if ag <= 0.35 else -10 if ag <= 0.50 else -20)
        else:
            # Standard evaluation for other sectors
            s += (25 if ag <= 0.05 else 15 if ag <= 0.10 else 0 if ag <= 0.20 else -15 if ag <= 0.30 else -25)
        c += 1
    else:
        s -= 5

    # Capex/Revenue (investing_flow_efficiency): Low Capex/Sales (<5% of sales) indicates efficient allocation
    # High Capex/Sales (>15-20% of sales) suggests potential overinvestment
    ife = winsorize(m.get('investing_flow_efficiency'), 0.0, 0.3)
    if ife is not None:
        if sector in ['technology', 'healthcare']:
            # Allow higher investment rates for R&D-intensive sectors
            s += (15 if ife <= 0.08 else 5 if ife <= 0.15 else -5 if ife <= 0.25 else -10)
        else:
            # Standard evaluation
            s += (15 if ife <= 0.05 else 5 if ife <= 0.10 else -5 if ife <= 0.15 else -15)
        c += 1
    else:
        s -= 5

    # Investment Flow Rate: Moderate reinvestment is optimal
    ifr = winsorize(m.get('investing_flow_rate'), 0.0, 1.0)
    if ifr is not None:
        s += (10 if 0.15 <= ifr <= 0.40 else 5 if ifr <= 0.15 else -10 if ifr > 0.60 else 0)
        c += 1
    else:
        s -= 5

    # Asset Turnover: High turnover (>1.0 sales-to-assets) indicates efficient resource use
    # Top quartile asset turnover shows higher ROIC
    at = winsorize(m.get('asset_turnover'), 0.0, 3.0)
    if at is not None:
        if sector in ['industrial', 'utility']:
            # Capital-intensive industries: expect low turnover, no penalty
            s += (25 if at >= 1.0 else 20 if at >= 0.6 else 10 if at >= 0.4 else 0 if at >= 0.2 else -5)
        else:
            # Standard evaluation
            s += (25 if at >= 1.5 else 20 if at >= 1.0 else 10 if at >= 0.8 else -10 if at < 0.5 else 0)
        c += 1
    else:
        s -= 5

    final = max(min(b + s, 100), 0) if c > 0 else 30
    if Config.DETAILED_LOGGING:
        print(f"Debug [{t}]: Investment Score = {final} (Count={c}, AG={ag}, IFE={ife}, IFR={ifr}, AT={at}, Sector={sector})")
    return final



def calculate_composite_quality(metrics, ticker):
    if not metrics: print(f"Error [{ticker}]: Metrics dictionary empty for composite score."); return 0

    # Get sector information for scoring adjustments
    sector = metrics.get('sector_info', {}).get('sector', 'general')

    # Updated weights based on research insights and user requirements:
    # Profitability 35%, Investment Quality 30%, Accounting Quality 20%, Payout Efficiency 15%
    # Research shows profitability and investment metrics are strongest predictors of future returns
    weights = {'profitability': 0.35, 'investment_quality': 0.30, 'accounting_quality': 0.20, 'payout_efficiency': 0.15}
    scores, total_w, comp_raw = {}, 0, 0

    # Define scoring functions with sector support
    score_funcs = {
        'profitability': lambda m, t: score_profitability(m, t, sector),
        'accounting_quality': score_accounting,  # No sector adjustments yet
        'payout_efficiency': lambda m, t: score_payout(m, t, sector),
        'investment_quality': lambda m, t: score_investment(m, t, sector)
    }

    missing_cats = [cat for cat in score_funcs if cat not in metrics or not isinstance(metrics.get(cat), dict)]
    if missing_cats and Config.DETAILED_LOGGING: print(f"Debug [{ticker}]: Missing categories for composite: {missing_cats}")

    for cat, func in score_funcs.items():
        if cat in metrics and isinstance(metrics[cat], dict):
            cat_s = func(metrics[cat], ticker)
            scores[cat] = cat_s
            if cat_s is not None:
                total_w += weights[cat]
                comp_raw += cat_s * weights[cat]
            else:
                scores[cat] = 'N/A'
        else:
            scores[cat] = 'N/A'

    comp_norm = safe_divide(comp_raw, total_w, 0)
    final_comp = max(0, min(comp_norm, 100))
    if Config.DETAILED_LOGGING:
        print(f"Debug [{ticker}]: Composite Score = {final_comp:.2f} (Raw={comp_raw:.2f}, Norm={comp_norm:.2f}, WeightSum={total_w:.2f}), Sector={sector}, breakdown={scores}")
    return final_comp

# --- Fetch Financial Data (Profile & Financials) ---
def fetch_financial_data(session, company):
    data = {'company': company, 'profile': {}, 'financials': []}
    try:
        print(f"Info [{company}]: Fetching profile...")
        resp = rate_limited_request(session, "GET", f"{Config.POLYGON_BASE_URL}/v3/reference/tickers/{company}")
        profile_data = resp.json().get('results', {})
        if profile_data.get('ticker'): data['profile'] = profile_data; print(f"Info [{company}]: Fetched profile for {profile_data.get('name', company)}.")
        else: print(f"Warning [{company}]: Profile data empty/invalid for {company}.")
    except Exception as e: print(f"Error [{company}]: Fetching profile failed: {e}")

    try:
        print(f"Info [{company}]: Fetching financials...")
        params = {"ticker": company, "limit": 6, "sort": "filing_date"}
        resp = rate_limited_request(session, "GET", f"{Config.POLYGON_BASE_URL}/vX/reference/financials", params=params)
        raw = resp.json().get('results', [])
        if raw and isinstance(raw, list) and len(raw) > 0 and 'financials' in raw[0]:
            data['financials'] = raw; print(f"Info [{company}]: Fetched {len(raw)} financial periods.")
        else: print(f"Warning [{company}]: Financials data issue for {company}. Results: {str(raw)[:100]}")
    except Exception as e: print(f"Error [{company}]: Fetching financials failed: {e}")
    return data

# --- Fetch Qualitative Data (News) ---
def fetch_qualitative_data(session, company):
    data = {"news": []}
    params = {"ticker": company, "limit": 50, "order": "desc"}
    try:
        print(f"Info [{company}]: Fetching news...")
        resp = rate_limited_request(session, "GET", f"{Config.POLYGON_BASE_URL}/v2/reference/news", params=params)
        news_results = resp.json().get('results', [])
        filtered_news, count, max_items, days_limit = [], 0, 20, 180
        if not news_results: print(f"Info [{company}]: No news items from API."); return data
        for n in news_results:
            if count >= max_items: break
            pub_utc, title = n.get("published_utc"), n.get("title")
            if pub_utc and title:
                try:
                    # Parse the date with timezone awareness
                    pub_date = parse(pub_utc)
                    # Get current time with timezone awareness
                    now = datetime.now().astimezone()
                    # Calculate days difference properly
                    if (now - pub_date).days <= days_limit:
                        filtered_news.append({"title": title, "published_utc": pub_utc, "article_url": n.get("article_url"), "description": n.get("description"), "insights": n.get("insights", []), "keywords": n.get("keywords", [])})
                        count += 1
                except Exception as e: print(f"Warning [{company}]: News date parse error '{pub_utc}': {e}")
        data["news"] = filtered_news
        print(f"Info [{company}]: Fetched {len(filtered_news)} news items (last {days_limit} days, max {max_items}).")
    except Exception as e: print(f"Error [{company}]: Fetching news failed: {e}")
    return data

def validate_fred_data(series_id, observations, name):
    """Validate FRED data for completeness and quality"""
    if not observations:
        print(f"Warning [FRED]: No observations found for {name} ({series_id})")
        return False

    # Check date range - only if we have observations
    if len(observations) < 2:
        print(f"Warning [FRED]: Not enough observations for {name} ({series_id}) to validate date range")
        return False

    try:
        start_date = datetime.strptime(observations[-1]['date'], '%Y-%m-%d')
        end_date = datetime.strptime(observations[0]['date'], '%Y-%m-%d')
        date_range = (end_date - start_date).days
    except (KeyError, IndexError, ValueError) as e:
        print(f"Warning [FRED]: Error parsing dates for {name} ({series_id}): {e}")
        return False

    # Expected frequency and minimum observations
    expected_freq = {
        # Series ID: (expected frequency in days, min observations expected)
        "T10Y3M": (1, 250),        # Daily - expect ~250 trading days in 1 year
        "SP500": (1, 250),         # Daily - expect ~250 trading days in 1 year
        "ICSA": (7, 52),           # Weekly - expect ~52 observations in 1 year
        "HOUST": (30, 12),         # Monthly - expect 12 observations in 1 year
        "AMTMNO": (30, 12),        # Monthly - expect 12 observations in 1 year
        "UMCSENT": (30, 12),       # Monthly - expect 12 observations in 1 year
        "PPIACO": (30, 12),        # Monthly - expect 12 observations in 1 year
        "PCETRIM12M159SFRBDAL": (30, 12), # Monthly - expect 12 observations in 1 year
        "DTWEXAFEGS": (1, 250),    # Daily - expect ~250 trading days in 1 year
    }

    # Get expected frequency for this series
    freq_days, min_obs = expected_freq.get(series_id, (30, 12))  # Default monthly, 12 obs

    # Check if we have enough observations
    if len(observations) < min_obs:
        print(f"Warning [FRED]: Only {len(observations)} observations for {name} ({series_id}), expected at least {min_obs}")

    # Check if date range is reasonable (at least 80% of 1 year)
    expected_range = 365  # 1 year in days
    if date_range < 0.8 * expected_range:
        print(f"Warning [FRED]: Date range for {name} ({series_id}) is only {date_range} days, expected at least {int(0.8 * expected_range)} days")

    # Check for gaps in data (only for higher frequency data)
    if freq_days <= 30:  # For monthly or higher frequency
        dates = [datetime.strptime(obs['date'], '%Y-%m-%d') for obs in observations]
        for i in range(1, len(dates)):
            gap = (dates[i-1] - dates[i]).days
            if gap > freq_days * 2.5:  # Allow for some flexibility
                print(f"Warning [FRED]: Possible gap in {name} ({series_id}) data between {dates[i].strftime('%Y-%m-%d')} and {dates[i-1].strftime('%Y-%m-%d')} ({gap} days)")

    return True

def fetch_economic_data_fred(session):
    if not Config.FRED_API_KEY: print("Warning: FRED_API_KEY not set. Skipping FRED economic data."); return {}
    print("Info: Fetching U.S. economic indicators from FRED...")
    fred_data = {}
    # Use 2 years of data for analysis
    start_date_str = (datetime.now() - relativedelta(years=1)).strftime('%Y-%m-%d')

    # Track successful and failed series
    successful_series = []
    failed_series = []

    for series_id, name in Config.FRED_SERIES_INFO.items():
        params = {
            "series_id": series_id,
            "api_key": Config.FRED_API_KEY,
            "file_type": "json",
            "observation_start": start_date_str,
            "observation_end": datetime.now().strftime('%Y-%m-%d'),
            "sort_order": "desc",
            "frequency": "" # Get all available frequencies
        }
        try:
            resp = rate_limited_request(session, "GET", f"{Config.FRED_BASE_URL}/series/observations", params=params, delay_override=Config.FRED_REQUEST_DELAY)
            # Filter out missing values and limit to display limit
            observations = [obs for obs in resp.json().get('observations', []) if obs.get('value') != "."]

            # Validate the data
            if observations and validate_fred_data(series_id, observations, name):
                # Limit to display limit after validation
                # observations = observations[:Config.FRED_OBSERVATIONS_DISPLAY_LIMIT] # User requested full period
                # Store with series ID as key for better reference
                fred_data[f"{name} ({series_id})"] = [{"date": o.get('date'), "value": float(o.get('value'))} for o in observations]
                successful_series.append(series_id)

                print(f"Info [FRED]: Successfully fetched {len(observations)} observations for {name} ({series_id}), from {observations[-1]['date']} to {observations[0]['date']}.")
            else:
                failed_series.append(series_id)
        except Exception as e:
            print(f"Error [FRED]: Fetching {name} ({series_id}) failed: {e}")
            failed_series.append(series_id)

    # Summary report
    if fred_data:
        print(f"Info: Successfully fetched data for {len(successful_series)}/{len(Config.FRED_SERIES_INFO)} FRED series covering 1-year period.")
        if failed_series:
            print(f"Warning: Failed to fetch data for {len(failed_series)} series: {', '.join(failed_series)}")
    else:
        print("Warning: No data fetched from FRED.")

    return fred_data

# --- Generate LLM Prompt ---
def generate_analysis_prompt(company, financial_data, quality_metrics, qualitative_data, fred_economic_data, valuation_data=None):
    """
    Generate a structured LLM prompt for investment analysis using Parlant-inspired prompt engineering patterns.

    This function creates a comprehensive prompt that guides the LLM through a rigorous 6-factor quality analysis
    using Warren Buffett's value investing principles. The prompt structure incorporates best practices from the
    Parlant framework to improve LLM response reliability and reduce parsing failures.

    Parlant-Inspired Enhancements (applied 2025):
    - Explicit term definitions (<Definitions> section) to reduce ambiguity
    - Few-shot example (<OutputExample> section) demonstrating exact output format
    - Structured instructions with clear hierarchical numbering and separation of objectives/processes
    - Output validation checklist (<OutputValidation> section) for LLM self-checking
    - Enhanced data section labeling with clear purpose statements
    - Final format reminder (<FinalReminder> section) emphasizing critical parsing requirements

    Args:
        company (str): Company ticker symbol
        financial_data (dict): Company profile and financial information from Polygon.io
        quality_metrics (dict): Calculated quality metrics including raw financial values
        qualitative_data (dict): News and qualitative information
        fred_economic_data (dict): U.S. macroeconomic indicators from FRED
        valuation_data (dict, optional): Valuation metrics from yfinance

    Returns:
        str: Formatted prompt string ready for LLM API call

    Note:
        The prompt maintains the original 6-factor Buffett analysis methodology while improving
        clarity and reliability through better prompt engineering. Expected prompt length: ~15-20k characters.
    """
    # Extract company information
    profile = financial_data.get('profile', {})
    raw_fin_latest = quality_metrics.get('raw_values_latest', {}) if quality_metrics else {}
    company_name = profile.get('name', company)
    sector = profile.get('sic_description', 'N/A')
    description = profile.get('description', 'N/A')
    ticker = profile.get('ticker', company)

    # Use enhanced formatting helpers (Parlant-inspired data adaptation pattern)
    profile_str = format_financial_data(profile)
    news_str = format_financial_data(qualitative_data.get('news', []))
    raw_fin_str = format_financial_data(raw_fin_latest)
    valuation_str = format_valuation_data(valuation_data)
    fred_str = format_economic_data(fred_economic_data)

    # Build data sections using enhanced PromptBuilder (Parlant-inspired section organization)
    current_date = datetime.now().strftime('%Y-%m-%d')
    data_builder = PromptBuilder(context_name="data_for_analysis")

    data_builder.add_context(
        "ANALYSIS_METADATA",
        f"Analysis Date: {current_date}\nCompany: {company_name} ({ticker})\nSector: {sector}"
    )

    data_builder.add_data_section(
        "COMPANY_PROFILE",
        profile_str,
        purpose="Basic company information, sector classification, and business description"
    )

    data_builder.add_data_section(
        "LATEST_FINANCIAL_VALUES",
        raw_fin_str,
        purpose="Most recent financial metrics for quantitative analysis (Source: Polygon.io /vX API)"
    )

    data_builder.add_data_section(
        "VALUATION_METRICS",
        valuation_str,
        purpose="Market valuation ratios and multiples for Factor 6 (Fair Price) analysis (Source: yfinance)"
    )

    data_builder.add_data_section(
        "RECENT_NEWS",
        news_str,
        purpose="Qualitative context on recent developments, strategic initiatives, and market sentiment"
    )

    data_builder.add_data_section(
        "MACROECONOMIC_CONTEXT",
        fred_str,
        purpose="Economic indicators for top-down macroeconomic analysis (Source: FRED)"
    )

    data_for_analysis = f"""
{'='*80}
DATA FOR ANALYSIS
{'='*80}

{data_builder.build()}

{'='*80}
"""

    # Prompt structure (simplified for brevity, full prompt is used in actual call)
    # The actual prompt is long and detailed as provided by the user initially.
    # This is just a placeholder to indicate where data_for_analysis is used.
    prompt = f"""
<Persona>
You are an expert-level Financial Analyst specializing in fundamental analysis. Your operating model is built on the principles of Warren Buffett's value investing philosophy, emphasizing deep, rigorous analysis over superficial metrics. You are methodical, skeptical, data-driven, and committed to intellectual honesty.

Your task is to evaluate {company_name} ({ticker}) by rigorously applying first-principles thinking and the Thesis-Antithesis-Synthesis framework (as defined in <Definitions>) to each of the 6 specified quality factors. Your analysis must be deeply reasoned, evidence-based, and avoid superficial observations.

ANALYTICAL APPROACH:
1. Begin with a top-down analysis of the U.S. macroeconomic environment using the provided FRED data
2. Integrate your knowledge of global supply chains, U.S. government policies, and sector-specific dynamics
3. Analyze {company_name}'s future performance prospects within this established macroeconomic context
4. Apply the structured analytical frameworks defined in <Definitions> and <Instructions>
5. Use the provided company data, financial metrics, valuation data, recent news, and your domain expertise

COMPANY OVERVIEW:
Name: {company_name} ({ticker})
Sector: {sector}
Description: {description}

{data_for_analysis}
</Persona>

<Frameworks>
You must operate under the following frameworks, which supersede all other instructions:
# --- Analysis and Reasoning Principles ---
Analysis-1: Distinction: You must strictly distinguish between objective factual statements (Observation) and your subjective logical interpretations (Inference).
Analysis-2: Grounding: Every inference must be explicitly tied back to a specific observation from the provided data or the established macroeconomic context.
</Frameworks>

<Definitions>
To ensure clarity and consistency, the following key terms are explicitly defined:

**Buffett Score**: A numerical rating on a scale from -100 to +100 that represents the quality assessment of a specific factor:
  - +100: Exceptionally strong, best-in-class performance with sustainable competitive advantages
  - +50 to +99: Strong performance with clear positive indicators
  - 0 to +49: Adequate performance with mixed signals
  - -1 to -49: Weak performance with concerning indicators
  - -50 to -99: Poor performance with significant red flags
  - -100: Critically weak, fundamental structural problems

**Thesis-Antithesis-Synthesis**: A dialectical reasoning framework consisting of three stages:
  1. Thesis: Construct a positive argument supported by evidence from data and analysis
  2. Antithesis: Challenge the thesis by identifying counter-arguments, risks, and alternative interpretations
  3. Synthesis: Reconcile the thesis and antithesis into a balanced, nuanced conclusion that acknowledges both strengths and weaknesses

**First-Principle Deconstruction**: An analytical method that involves:
  1. Breaking down a complex factor into its most fundamental, irreducible components
  2. Questioning each component by asking "Why is this the case?" and "What is the underlying driver?"
  3. Identifying core causal relationships rather than accepting surface-level correlations
  4. Building understanding from foundational truths rather than assumptions or analogies

**Harmonic Mean**: A mathematical average calculated as N divided by the sum of reciprocals (N / Σ(1/xi)), used to aggregate the 6 Buffett Scores. The harmonic mean is more sensitive to low values than the arithmetic mean, ensuring that weakness in any single factor significantly impacts the overall score.
</Definitions>

<Instructions>
Your analysis must follow this structured process in the exact order specified:

**STEP 1: U.S. Macroeconomic Environment Analysis (Top-Down)**
Objective: Establish the macroeconomic context that will inform all subsequent factor analyses.

Process:
1.1. Create a Diffusion Index:
     - For each indicator in 'U.S. Macroeconomic Context Data' (from FRED), apply smoothing by converting daily/weekly data to monthly averages
     - Determine trend direction by comparing current values to their 3-month moving average (above = up trend, below = down trend)
     - Count how many indicators are improving versus deteriorating to create the diffusion index
     - Use this quantitative foundation to evaluate the current state and trajectory of the overall macroeconomic environment

1.2. Integrate Broader Factors:
     - Incorporate the influence of global supply chains on {company_name}'s sector
     - Consider relevant U.S. government policies and their impact
     - Assess geopolitical factors affecting the business environment

1.3. Synthesize Macroeconomic Impact:
     - Conclude with a summary of the overall macroeconomic climate (e.g., expansionary, contractionary, inflationary, recessionary risk)
     - Articulate the *fundamental impacts* these conditions are likely to have on businesses operating in {company_name}'s sector
     - Specify how these conditions will affect {company_name} directly

Expected Output: Internal analysis only (not included in final output to save tokens). This context informs all subsequent factor scoring.

---

**STEP 2: Analysis of 6 Quality Factors (Iterative Process for EACH Factor)**
Objective: Evaluate {company_name} on each of the 6 quality factors using rigorous analytical frameworks.

Process for EACH of the 6 factors:

2.A. Contextual Link:
     - Briefly state how the macroeconomic environment (from Step 1) specifically creates opportunities or challenges for {company_name} concerning *this particular factor*
     - Connect macro conditions to factor-specific implications

2.B. First-Principle Deconstruction (as defined in <Definitions>):
     - Break down *this factor* as it applies to {company_name} into its most fundamental components and assumptions
     - Question these components by repeatedly asking "Why is this the case?" or "What is the underlying driver here?"
     - Use the provided company data, recent news, and your knowledge of the company and industry characteristics
     - Identify the core causal relationships and evidence supporting them, moving beyond surface-level observations or correlations

2.C. Thesis-Antithesis-Synthesis Application (as defined in <Definitions>):
     i. Thesis: Based on your first-principle deconstruction, construct a clear, evidence-backed positive argument (the "thesis") for {company_name}'s strength or potential regarding *this factor*

     ii. Antithesis: Challenge the thesis. Identify and articulate potential weaknesses, risks, counter-arguments, or alternative interpretations (the "antithesis") related to *this factor*, again grounded in your first-principle analysis, company data, and the macro context

     iii. Synthesis & Scoring:
          - Reconcile the thesis and antithesis to form a nuanced, balanced, and well-reasoned conclusion (the "synthesis") about {company_name}'s standing on *this factor*
          - To calibrate the score using semantic steering, internally generate three steered syntheses: a cautious (antithesis-leaning), neutral (balanced), and confident (thesis-leaning) version
          - Aggregate them by computing a consistency-weighted average, where consistency is based on alignment across the steered outputs, to finalize the calibrated Buffett Score
          - Based *explicitly* on this synthesis and calibration, assign a "Buffett Score" from -100 (critically weak) to +100 (exceptionally strong) using the scale defined in <Definitions>

2.D. Internal Reasoning:
     - While performing steps 2.A through 2.C, conduct this analysis internally
     - Your reasoning process should inform the score but does NOT need to be included in the final output (to save tokens)

Expected Output: For each factor, output ONLY the factor name and its Buffett Score in the exact format shown in <OutputExample>.

---

**STEP 3: Calculate Final Score and Format Output**
Objective: Aggregate the 6 factor scores and produce the final output in the exact required format.

Process:
3.1. Calculate the harmonic mean of the 6 Buffett Scores (as defined in <Definitions>)
3.2. Round the result to two decimal places
3.3. Format your complete response EXACTLY as shown in <OutputExample>

Expected Output: The complete formatted response with all 6 factors and the final score section.

---

**CRITICAL FORMAT REQUIREMENTS:**
- Produce ONLY the output specified in <required_format> and demonstrated in <OutputExample>
- Do NOT include explanations, reasoning, or analysis text in the final output (save tokens)
- Output ONLY: Factor names, Buffett Scores, and the final [llm_qualitative_score] section
- The line "llm_qualitative_score: [score]/100" must be EXACT with no variations
</Instructions>

<6_quality_factors>
Guidance: Your detailed reasoning based on <Instructions> 2.A, 2.B, 2.C.i, 2.C.ii, 2.C.iii
1.  SUSTAINABLE COMPETITIVE ADVANTAGE (ECONOMIC MOAT): Fundamental drivers protecting long-term profitability and market share.
    Key Aspects: Brand Strength & Recognition, Network Effects, Cost Advantages, High Switching Costs, Patents & Intellectual Property, Regulatory Barriers, Efficient Scale.

2.  INNOVATION CAPACITY & R&D EFFECTIVENESS: Ability to generate future growth through innovation.
    Key Aspects: Efficient & Productive R&D Investment, Strong Patent/IP Portfolio, Adaptability & Speed to Market, Positive Innovation Geography/Cluster Effects.

3.  CORPORATE GOVERNANCE & ETHICS: Systems ensuring accountability, fairness, transparency, and ethical behavior.
    Key Aspects: Board Independence & Effectiveness, Strong Shareholder Rights, Transparent Reporting & Accountability, Clean Ethical Track Record/Minimal Scandals, Effective CEO Succession Planning.

4.  FINANCIAL REPORTING QUALITY & TRANSPARENCY: Clarity, reliability, and timeliness of financial disclosures.
    Key Aspects: Transparent & Understandable Reporting, Consistent & Persistent Earnings (less manipulation), Timely Recognition of Economic Events, Strong Cash Flow Conversion relative to Earnings.

5.  COMPETITIVE POSITION & PRICING POWER: The company's standing relative to rivals and ability to command favorable pricing.
    Key Aspects: Positive Market Share Trends (relative to industry growth), Demonstrated Pricing Power, Favorable Industry Structure (less intense competition).

6.  WONDERFUL BUSINESSES AT FAIR PRICES: Considering its industry characteristics and historical valuation metrics, compare the intrinsic value to the current market price to determine if the stock is undervalued, fairly valued,or overvalued. You must heavily penalize undervalued mediocre companies or overvalued high-quality companies. Prefer fairly valued good companies.
    Key Aspects: Intrinsic Value Analysis based on its ability to generate future cash flows and compound value over time, Margin of Safety, Fairness of Current Market Price. Considering market mispricing opportunities. Prioritizing quality over buying mediocre companies at deep discounts.
</6_quality_factors>

<OutputExample>
Below is a concrete example of the EXACT format your response must follow. This example uses a fictional company for illustration purposes only:

EXAMPLE OUTPUT:
---
1. SUSTAINABLE COMPETITIVE ADVANTAGE (ECONOMIC MOAT):
   Buffett Score: 45
2. INNOVATION CAPACITY & R&D EFFECTIVENESS:
   Buffett Score: 32
3. CORPORATE GOVERNANCE & ETHICS:
   Buffett Score: 58
4. FINANCIAL REPORTING QUALITY & TRANSPARENCY:
   Buffett Score: 41
5. COMPETITIVE POSITION & PRICING POWER:
   Buffett Score: 37
6. WONDERFUL BUSINESSES AT FAIR PRICES:
   Buffett Score: 29

[llm_qualitative_score]
   -Rating: 38.42
   llm_qualitative_score: 38.42/100
---

CRITICAL: Your actual response must follow this EXACT structure with:
- Each factor numbered 1-6 with its full name
- "Buffett Score: [number]" on the line immediately after each factor name
- The [llm_qualitative_score] section at the end
- The exact line "llm_qualitative_score: [score]/100" where [score] is the harmonic mean of the 6 factor scores
</OutputExample>

<required_format>
1. SUSTAINABLE COMPETITIVE ADVANTAGE (ECONOMIC MOAT):
   Buffett Score: [Score]
2. INNOVATION CAPACITY & R&D EFFECTIVENESS:
   Buffett Score: [Score]
3. CORPORATE GOVERNANCE & ETHICS:
   Buffett Score: [Score]
4. FINANCIAL REPORTING QUALITY & TRANSPARENCY:
   Buffett Score: [Score]
5. COMPETITIVE POSITION & PRICING POWER:
    Buffett Score: [Score]
6. WONDERFUL BUSINESSES AT FAIR PRICES:
    Buffett Score: [Score]

[llm_qualitative_score]
   -Rating: [Harmonic mean of the 6 factor scores, rounded to two decimal]
   llm_qualitative_score: [score]/100  <-- IMPORTANT: Include this exact line and exact format.
</required_format>

<OutputValidation>
Before finalizing your response, perform the following self-checks to ensure format compliance:

VALIDATION CHECKLIST:
☐ 1. All 6 factors are numbered 1-6 with their complete names exactly as shown in <required_format>
☐ 2. Each factor has "Buffett Score: [number]" on the line immediately following the factor name
☐ 3. All Buffett Scores are integers between -100 and +100
☐ 4. The [llm_qualitative_score] section appears after all 6 factors
☐ 5. The "-Rating:" line shows the harmonic mean rounded to two decimal places
☐ 6. The CRITICAL line "llm_qualitative_score: [score]/100" is present with EXACT formatting (no spaces before "llm", colon after "score", "/100" at the end)
☐ 7. No additional text, explanations, or commentary appears after the required format
☐ 8. The harmonic mean calculation is correct: N=6, formula = 6 / (1/score1 + 1/score2 + ... + 1/score6)

# --- Phase 2 (Option A) JSON-first prompt and rendering ---


















If any checklist item is not satisfied, revise your output before submitting.
</OutputValidation>

<FinalReminder>
CRITICAL PARSING REQUIREMENTS - Your response will be parsed by automated systems:

1. The EXACT line "llm_qualitative_score: [score]/100" MUST appear in your output
2. The text "Buffett Score:" MUST appear 6 times (once for each factor)
3. Do NOT include any text after the [llm_qualitative_score] section
4. Do NOT add explanatory text, reasoning, or analysis in the final output
5. Follow the <OutputExample> structure EXACTLY

Your response should contain ONLY the formatted scores as demonstrated in <OutputExample>.
</FinalReminder>

"""

    # Build final prompt using enhanced PromptBuilder (Parlant-inspired structured composition)
    builder = PromptBuilder(on_build=log_prompt_sections, context_name="stock_analysis")

    # Add main prompt content
    builder.add_instructions("ANALYSIS_PROMPT", prompt)

    # Build sector-specific guidelines (deterministic injection based on company profile)
    guidelines = [
        "Follow <required_format> exactly; do not include any extra text.",
        "Ensure exactly 6 occurrences of 'Buffett Score:' appear (one per factor).",
        "Include the exact line 'llm_qualitative_score: [score]/100' at the end.",
        "Before finalizing, self-check against <OutputValidation> and fix issues before submitting.",
        "If data is missing/ambiguous, avoid speculation; prefer conservative scoring; do not invent facts.",
    ]

    # Add sector-specific guidelines based on company profile
    try:
        profile_data = (financial_data or {}).get('company_profile', {})
        profile_text = " ".join(str(v) for v in profile_data.values()).lower()

        if any(k in profile_text for k in ["reit", "real estate investment trust"]):
            guidelines.append("REITs: 90%+ payout ratios are normal; ~5% ROE can be acceptable.")
        if any(k in profile_text for k in ["utility", "utilities"]):
            guidelines.append("Utilities: 60-70% payout ratios; 9-10% ROE typical; regulated returns considered.")
        if any(k in profile_text for k in ["technology", "software", "semiconductor", "biotech", "healthcare", "pharma"]):
            guidelines.append("Tech/Healthcare: Use R&D-adjusted metrics when evaluating profitability and margins.")
        if any(k in profile_text for k in ["bank", "financial", "insurance", "brokerage"]):
            guidelines.append("Financials: Emphasize cash flow quality; interpret leverage differently from industrials.")
        if any(k in profile_text for k in ["steel", "mining", "oil", "gas", "manufacturing", "refining", "chemicals", "telecom infrastructure"]):
            guidelines.append("Capital-intensive: Lower asset turnover expected; do not penalize purely for high capital intensity.")
    except Exception as e:
        if Config.DETAILED_LOGGING:
            print(f"Warning: Failed to extract sector-specific guidelines: {e}")

    # Add guidelines section
    builder.add_instructions("SECTOR_GUIDELINES", "\n".join(f"- {g}" for g in guidelines))

    # Build and return final prompt
    final_prompt = builder.build()
    return final_prompt

# --- Get LLM Analysis ---
def get_llm_analysis(prompt, ticker):
    if not Config.OPENROUTER_API_KEY or not Config.OPENROUTER_URL:
        print(f"Warning [{ticker}]: LLM API key/URL missing. Skipping LLM analysis."); return "LLM analysis skipped."
    for attempt in range(Config.LLM_RETRIES):
        try:
            headers = {"Authorization": f"Bearer {Config.OPENROUTER_API_KEY}", "Content-Type": "application/json", "HTTP-Referer": "http://localhost", "X-Title": "QualityX"}
            # Max prompt length check (OpenRouter has limits, e.g. 200k tokens for some models, characters are a rough proxy)
            # A more robust solution would be token counting if specific model limits are critical.
            # For now, a character limit is a basic safeguard.
            # Typical token to char ratio is ~1:4. 100k chars ~ 25k tokens.
            # Max prompt length for Deepseek Coder R1 is 128k tokens.
            # Let's use a generous char limit, e.g., 400,000 chars for prompt.
            max_prompt_chars = 400000
            if len(prompt) > max_prompt_chars:
                print(f"Warning [{ticker}]: Prompt length ({len(prompt)} chars) exceeds limit ({max_prompt_chars}). Truncating.")
                # Truncate safely at a newline or space to avoid cutting in the middle of a UTF-8 sequence
                safe_end = max_prompt_chars
                while safe_end > max_prompt_chars - 100 and safe_end > 0:
                    if prompt[safe_end] in ['\n', ' ', '.', '!', '?']:
                        break
                    safe_end -= 1
                # If we couldn't find a safe breakpoint, use a slightly smaller limit
                if safe_end <= max_prompt_chars - 100:
                    safe_end = max_prompt_chars - 10
                prompt = prompt[:safe_end]

            # Use OptimizationPolicy for temperature progression (Parlant-inspired retry strategy)
            temp = OptimizationPolicy.get_temperature(attempt, Config.LLM_RETRIES, strategy="conservative")
            payload = {"model": Config.MODEL_NAME, "messages": [{"role": "user", "content": prompt}], "max_tokens": 50000, "top_k": 20, "top_p": 0.9, "temperature": temp}
            print(f"Info [{ticker}]: Sending request to LLM (attempt {attempt+1}/{Config.LLM_RETRIES}, temp={temp:.2f}). Model: {Config.MODEL_NAME}, Prompt length: {len(prompt)} chars.")
            # Set separate connect and read timeouts for better control
            connect_timeout = min(30, Config.LLM_TIMEOUT / 5)  # 30 seconds or 1/5 of total timeout
            read_timeout = Config.LLM_TIMEOUT - connect_timeout  # Remaining time for reading
            response = requests.post(Config.OPENROUTER_URL, headers=headers, json=payload,
                                   timeout=(connect_timeout, read_timeout))
            response.raise_for_status()
            data = response.json()

            if "error" in data: print(f"Error [{ticker}]: LLM API error: {data['error'].get('message', data['error'])}")
            elif data.get("choices") and data["choices"][0].get("message") and "content" in data["choices"][0]["message"]:
                analysis = data["choices"][0]["message"]["content"]
                finish_reason = data["choices"][0].get("finish_reason", "N/A")
                print(f"Info [{ticker}]: LLM Response Received (length: {len(analysis)} chars, finish_reason: {finish_reason})")
                if finish_reason == "length": print(f"Warning [{ticker}]: LLM response may be truncated.")

                # Validate response strictly: require 6 'Buffett Score:' and valid llm_qualitative_score line
                valid, reason = validate_llm_text(analysis)
                if not valid:
                    print(f"Warning [{ticker}]: LLM response validation failed ({reason}). Will retry.")
                    # fall through to retry
                else:
                    return analysis.strip()
            else: print(f"Error [{ticker}]: LLM response structure unexpected: {str(data)[:200]}")
        except requests.exceptions.Timeout: print(f"Warning [{ticker}]: LLM request timed out (attempt {attempt+1}).")
        except requests.exceptions.RequestException as e: print(f"Error [{ticker}]: LLM request error (attempt {attempt+1}): {e}")
        except Exception as e: print(f"Error [{ticker}]: Unexpected error in LLM analysis (attempt {attempt+1}): {e}"); import traceback; traceback.print_exc()

        # Retry if we haven't exceeded the maximum number of attempts
        if attempt < Config.LLM_RETRIES - 1:
            print(f"Info [{ticker}]: Retrying LLM call in {Config.LLM_RETRY_DELAY}s...")
            time.sleep(Config.LLM_RETRY_DELAY)
    return "LLM analysis failed after multiple attempts."


# --- Phase 2 (Option A) JSON-first prompt and rendering ---

def generate_json_scoring_prompt(company, financial_data, quality_metrics, qualitative_data, fred_economic_data, valuation_data=None):
    """
    Build a compact prompt for JSON-only scoring using enhanced PromptBuilder.

    Uses Parlant-inspired structured sections for clear data presentation.
    """
    pb = PromptBuilder(on_build=log_prompt_sections, context_name="json_scoring")

    # Add role/instructions
    pb.add_instructions(
        "ROLE",
        "You are a disciplined equity analyst. Compute 6 Buffett-style factor scores (0-100) and their harmonic mean."
    )

    # Add data sections using enhanced methods
    pb.add_data_section(
        "COMPANY_PROFILE",
        format_financial_data(financial_data.get('company_profile', {})),
        purpose="Company identification and sector information"
    )

    pb.add_data_section(
        "FINANCIALS_LATEST",
        format_financial_data(financial_data.get('financials', [])[:3]),
        purpose="Recent financial statements for quantitative analysis"
    )

    if valuation_data:
        pb.add_data_section(
            "VALUATION_METRICS",
            format_valuation_data(valuation_data),
            purpose="Market valuation ratios"
        )

    if qualitative_data:
        pb.add_data_section(
            "RECENT_NEWS",
            format_financial_data(qualitative_data.get('news', [])[:8]),
            purpose="Recent news for qualitative context"
        )

    if fred_economic_data:
        pb.add_data_section(
            "MACROECONOMIC_DATA",
            format_economic_data(fred_economic_data),
            purpose="U.S. economic indicators"
        )

    # Add output instructions
    pb.add_instructions(
        "FACTORS",
        "Return ONLY JSON with fields: scores[6] and llm_qualitative_score. Order factors: Moat, Innovation, Governance, Reporting, Position, Price."
    )

    schema_text = {
        "type": "object",
        "required": ["scores", "llm_qualitative_score"],
        "properties": {
            "scores": {"type": "array", "items": {"type": "number"}, "minItems": 6, "maxItems": 6},
            "llm_qualitative_score": {"type": "number"}
        }
    }

    pb.add_instructions(
        "OUTPUT_SCHEMA",
        f"Return JSON ONLY with this schema (no extra keys, no prose): {json.dumps(schema_text, ensure_ascii=False)}"
    )

    pb.add_instructions(
        "FORMAT_RULES",
        "No markdown, no explanations. JSON only. Numbers 0..100. Round to 2 decimals if needed."
    )

    return pb.build()

def get_llm_scores_json(prompt_json: str, ticker: str):
    """Get JSON scores from LLM with enhanced retry logic (Parlant-inspired)."""
    for attempt in range(Config.LLM_RETRIES):
        try:
            # Use OptimizationPolicy for temperature progression
            temp = OptimizationPolicy.get_temperature(attempt, Config.LLM_RETRIES, strategy="conservative")
            headers = {"Authorization": f"Bearer {Config.OPENROUTER_API_KEY}", "Content-Type": "application/json", "HTTP-Referer": "http://localhost", "X-Title": "QualityX"}
            payload = {"model": Config.MODEL_NAME, "messages": [{"role": "user", "content": prompt_json}], "max_tokens": 2000, "temperature": temp}

            if Config.DETAILED_LOGGING:
                print(f"Info [{ticker}]: JSON scoring attempt {attempt+1}/{Config.LLM_RETRIES}, temp={temp:.2f}")

            response = requests.post(Config.OPENROUTER_URL, headers=headers, json=payload, timeout=Config.LLM_TIMEOUT)
            response.raise_for_status()
            data = response.json()
            if data.get("choices") and data["choices"][0].get("message") and "content" in data["choices"][0]["message"]:
                content = data["choices"][0]["message"]["content"].strip()
                start = content.find('{'); end = content.rfind('}')
                if start != -1 and end != -1 and end > start:
                    content = content[start:end+1]
                try:
                    obj = json.loads(content)
                    ok, info = validate_json_scores(obj)
                    if ok:
                        if info: print(f"Warning [{ticker}]: JSON scores valid with note: {info}")
                        return obj
                    else:
                        print(f"Warning [{ticker}]: JSON validation failed: {info}")
                except Exception as e:
                    print(f"Warning [{ticker}]: JSON parse error: {e}")
            else:
                print(f"Error [{ticker}]: Unexpected JSON LLM response structure")
        except Exception as e:
            print(f"Error [{ticker}]: LLM JSON request error (attempt {attempt+1}): {e}")
        if attempt < Config.LLM_RETRIES - 1:
            print(f"Info [{ticker}]: Retrying JSON call in {Config.LLM_RETRY_DELAY}s...")
            time.sleep(Config.LLM_RETRY_DELAY)
    return None

def generate_text_render_prompt_from_json(json_payload: dict):
    required_format = (
        "1. SUSTAINABLE COMPETITIVE ADVANTAGE (ECONOMIC MOAT):\n   Buffett Score: [Score]\n"
        "2. INNOVATION CAPACITY & R&D EFFECTIVENESS:\n   Buffett Score: [Score]\n"
        "3. CORPORATE GOVERNANCE & ETHICS:\n   Buffett Score: [Score]\n"
        "4. FINANCIAL REPORTING QUALITY & TRANSPARENCY:\n   Buffett Score: [Score]\n"
        "5. COMPETITIVE POSITION & PRICING POWER:\n    Buffett Score: [Score]\n"
        "6. WONDERFUL BUSINESSES AT FAIR PRICES:\n    Buffett Score: [Score]\n\n"
        "[llm_qualitative_score]\n   -Rating: [Harmonic mean of the 6 factor scores, rounded to two decimal]\n   llm_qualitative_score: [score]/100"
    )
    instructions = (
        "Render EXACTLY the required format using these numbers.\n"
        "- Do not include any commentary, markdown, or extra text.\n"
        "- Replace [Score]s with the 6 scores in order; replace [score] with llm_qualitative_score.\n"
        "- Ensure 'Buffett Score:' appears exactly 6 times, once after each factor line.\n"
        "- Include the exact line 'llm_qualitative_score: [score]/100'."
    )
    return (
        f"JSON INPUT (scores, llm_qualitative_score):\n{json.dumps(json_payload)}\n\n"
        f"REQUIRED_FORMAT:\n{required_format}\n\n"
        f"INSTRUCTIONS:\n{instructions}"
    )

def get_llm_render_text_from_json(json_payload: dict, ticker: str):
    """Render text from JSON scores with enhanced retry logic (Parlant-inspired)."""
    prompt = generate_text_render_prompt_from_json(json_payload)
    for attempt in range(Config.LLM_RETRIES):
        try:
            # Use OptimizationPolicy for temperature progression
            temp = OptimizationPolicy.get_temperature(attempt, Config.LLM_RETRIES, strategy="conservative")
            headers = {"Authorization": f"Bearer {Config.OPENROUTER_API_KEY}", "Content-Type": "application/json", "HTTP-Referer": "http://localhost", "X-Title": "QualityX"}
            payload = {"model": Config.MODEL_NAME, "messages": [{"role": "user", "content": prompt}], "max_tokens": 1000, "temperature": temp}

            if Config.DETAILED_LOGGING:
                print(f"Info [{ticker}]: Text rendering attempt {attempt+1}/{Config.LLM_RETRIES}, temp={temp:.2f}")

            response = requests.post(Config.OPENROUTER_URL, headers=headers, json=payload, timeout=Config.LLM_TIMEOUT)
            response.raise_for_status()
            data = response.json()
            if data.get("choices") and data["choices"][0].get("message") and "content" in data["choices"][0]["message"]:
                text = data["choices"][0]["message"]["content"]
                valid, reason = validate_llm_text(text)
                if valid:
                    return text.strip()
                else:
                    print(f"Warning [{ticker}]: Rendered text failed validation: {reason}")
            else:
                print(f"Error [{ticker}]: Unexpected render response structure")
        except Exception as e:
            print(f"Error [{ticker}]: LLM render request error (attempt {attempt+1}): {e}")
        if attempt < Config.LLM_RETRIES - 1:
            print(f"Info [{ticker}]: Retrying render call in {Config.LLM_RETRY_DELAY}s...")
            time.sleep(Config.LLM_RETRY_DELAY)
    return None

def run_two_phase_analysis(company, financial_data, quality_metrics, qualitative_data, fred_economic_data, valuation_data):
    json_prompt = generate_json_scoring_prompt(company, financial_data, quality_metrics, qualitative_data, fred_economic_data, valuation_data)
    json_obj = get_llm_scores_json(json_prompt, company)
    if not json_obj:
        print(f"Warning [{company}]: Falling back to single-call textual generation due to JSON phase failure.")
        prompt = generate_analysis_prompt(company, financial_data, quality_metrics, qualitative_data, fred_economic_data, valuation_data)
        return get_llm_analysis(prompt, company)
    else:
        print(f"Info [{company}]: JSON phase validated; proceeding to render phase.")
    try:
        scores = [max(0.0, min(100.0, float(s))) for s in json_obj.get('scores', [])]
        denom = sum(1.0 / max(1e-9, s) for s in scores)
        hm = 6.0 / denom if denom > 0 else 0.0
        json_obj['llm_qualitative_score'] = round(hm, 2)
        json_obj['scores'] = [round(s, 2) for s in scores]
    except Exception:
        pass
    text = get_llm_render_text_from_json(json_obj, company)
    if text:
        print(f"Info [{company}]: Render phase validated.")
        return text
    print(f"Warning [{company}]: Falling back to single-call textual generation due to render phase failure.")
    prompt = generate_analysis_prompt(company, financial_data, quality_metrics, qualitative_data, fred_economic_data, valuation_data)
    return get_llm_analysis(prompt, company)

# --- Parse LLM Score ---
def parse_qualitative_score(text, ticker):
    if not text or "LLM analysis failed" in text or "LLM analysis skipped" in text:
        return 0

    # Try JSON first if present
    try:
        parsed = json.loads(text)
        if isinstance(parsed, dict) and 'llm_qualitative_score' in parsed:
            score = float(parsed['llm_qualitative_score'])
            if -100 <= score <= 100:
                print(f"Info [{ticker}]: Parsed LLM Score (JSON): {score}")
                return score
            else:
                print(f"Warning [{ticker}]: JSON llm_qualitative_score {score} out of range. Using 0.")
                return 0
    except Exception:
        pass

    # Fallback to textual patterns
    patterns = [r"llm_qualitative_score:\s*([-+]?\d+(?:\.\d+)?)\s*/\s*100", r"-\s*Rating:\s*([-+]?\d+(?:\.\d+)?)"]
    for i, p_str in enumerate(patterns):
        match = re.search(p_str, text, re.IGNORECASE | re.MULTILINE)
        if match:
            try:
                score = float(match.group(1))
                if -100 <= score <= 100:
                    print(f"Info [{ticker}]: Parsed LLM Score{' (fallback)' if i > 0 else ''}: {score}")
                    return score
                else:
                    print(f"Warning [{ticker}]: Parsed LLM score {score} out of range. Using 0.")
            except ValueError:
                print(f"Error [{ticker}]: Could not convert LLM score '{match.group(1)}'. Using 0.")
            return 0  # Return 0 if parsing fails or score out of range after a match
    print(f"Error [{ticker}]: Could not find LLM score line. Using 0.")
    if Config.DETAILED_LOGGING: print(f"--- LLM Response for {ticker} (score parsing failed) ---\n{text[:500]}...\n--- End LLM Response ---")
    return 0

# --- Process Single Company ---
def process_company(session, company, fred_data_global):
    jt = JourneyTracker("company-analysis", company)
    jt.step("start")

    qm, analysis, llm_s, base_s, final_s = None, "", 0, 0, 0
    try:
        print(f"\n--- Starting analysis for {company} ---")
        fin_data = fetch_financial_data(session, company)
        qual_data = fetch_qualitative_data(session, company)

        if not fin_data.get('financials') or len(fin_data['financials']) < 2:
            print(f"Analysis failed [{company}]: Insufficient financial data."); save_analysis(company, "Failed: Insufficient financial data.", {"quality_score": 0}); return False

        qm = calculate_quality_metrics(fin_data, session)
        if qm is None:
            print(f"Analysis failed [{company}]: Could not calculate quality metrics."); save_analysis(company, "Failed: Could not calculate metrics.", {"quality_score": 0}); return False

        base_s = qm.get('base_quality_score', 0)
        print(f"Info [{company}]: Calculated Base Quantitative Score: {base_s:.2f}")

        # Pre-flight: ensure valuation metrics are fresh (bounded wait)
        valuation_data = ensure_valuation_metrics_fresh(company)
        if valuation_data:
            print(f"Info [{company}]: Successfully fetched valuation metrics from yfinance")
        else:
            print(f"Warning [{company}]: Proceeding without valuation metrics due to pre-flight timeout")



        if getattr(Config, 'USE_TWO_PHASE_LLM', True):
            jt.step("llm_two_phase_start")
            analysis = run_two_phase_analysis(company, fin_data, qm, qual_data, fred_data_global, valuation_data)
            jt.step("llm_two_phase_done")
        else:
            jt.step("llm_single_start")
            prompt = generate_analysis_prompt(company, fin_data, qm, qual_data, fred_data_global, valuation_data)
            if Config.DETAILED_LOGGING: print(f"\n--- PROMPT FOR {company} (first 500 chars) START ---\n{prompt[:500]}...\n--- PROMPT FOR {company} END ---\n")
            analysis = get_llm_analysis(prompt, company)
            jt.step("llm_single_done")
        jt.step("llm_response_received")


        llm_failed_skipped = "LLM analysis failed" in analysis or "LLM analysis skipped" in analysis or not analysis.strip()
        if llm_failed_skipped:
            print(f"Warning [{company}]: LLM analysis failed/skipped. Final score based 40% on Quant score only.")
            if "LLM analysis failed" not in analysis and "LLM analysis skipped" not in analysis: analysis += "\n\n*** LLM ANALYSIS FAILED/SKIPPED ***"
            llm_s = 0; final_s = base_s * 0.4
        else:
            llm_s = parse_qualitative_score(analysis, company)
            final_s = (base_s * 0.3) + (llm_s * 0.7)

        qm['llm_qualitative_score'] = llm_s
        qm['quality_score'] = round(max(0, min(final_s, 100)), 2)

        filename = save_analysis(company, analysis, qm)
        print(f"Success [{company}]: Analysis saved to {filename} (Final Score: {qm.get('quality_score', 'N/A')})")
        return True
    except Exception as e:
        print(f"--- Analysis failed for {company}: Unexpected error in process_company: {e} ---"); import traceback; traceback.print_exc()
        final_qm = qm if qm else {}
        final_qm['llm_qualitative_score'] = final_qm.get('llm_qualitative_score', llm_s)
        if 'quality_score' not in final_qm:
            b = final_qm.get('base_quality_score', 0); l = final_qm.get('llm_qualitative_score', 0)
            # Use the same logic as in the normal flow to determine if LLM analysis failed
            llm_failed_skipped = "LLM analysis failed" in analysis or "LLM analysis skipped" in analysis or not analysis.strip()
            f_guess = (b * 0.3) + (l * 0.7 if not llm_failed_skipped else 0)
            final_qm['quality_score'] = round(max(0, min(f_guess, 100)), 2)

        err_msg = f"Analysis failed: {e}\n\nLLM Output (if any):\n{analysis if analysis else 'N/A'}\n\nPartial metrics below:"
        save_analysis(company, err_msg, final_qm)
        return False




def validate_json_scores(payload: dict):
    if not isinstance(payload, dict):
        return False, "Not a JSON object"
    if "scores" not in payload or "llm_qualitative_score" not in payload:
        return False, "Missing required keys"
    scores = payload["scores"]
    if not isinstance(scores, list) or len(scores) != 6:
        return False, "scores must be an array of length 6"
    try:
        scores = [float(s) for s in scores]
    except Exception:
        return False, "scores must be numeric"
    for s in scores:
        if not (0 <= s <= 100):
            return False, f"score out of range: {s}"
    try:
        q = float(payload["llm_qualitative_score"])
    except Exception:
        return False, "llm_qualitative_score must be numeric"
    if not (0 <= q <= 100):
        return False, "llm_qualitative_score out of range"
    try:
        denom = sum(1.0 / max(1e-9, s) for s in scores)
        hm = 6.0 / denom if denom > 0 else 0.0
        if abs(hm - q) > 2.0:
            return True, f"Harmonic mean differs by {abs(hm-q):.2f} (computed {hm:.2f} vs provided {q:.2f})"
    except Exception:
        pass
    return True, None















# --- Save Analysis Report ---
def save_analysis(company, analysis_text, quality_metrics):
    qm = quality_metrics if quality_metrics else {}
    final_score = qm.get('quality_score', 0)
    safe_company = re.sub(r'[\\/*?:"<>|]', "", company)
    filename = f"{safe_company}_{final_score:.2f}.txt"
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"Analysis Report for {company}\nGenerated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Final Quality Score: {final_score:.2f}/100 (Quant: 40%, Qual: 60%)\n{'='*80}\n\n")
            f.write(f"Qualitative Analysis (LLM Output):\n{'-'*35}\n{analysis_text if analysis_text else 'N/A'}\n\n{'='*80}\n")
            f.write(f"Quantitative Metrics Summary:\n{'-'*35}\n")
            f.write(f"*** Notes: Polygon.io /vX Financials. N/A if data missing/non-numeric. Accruals (CF Method). Investment (Inv CF proxy). Payout/Dilution attempted (often N/A). ***\n\n")

            f.write(f"Base Quantitative Score: {qm.get('base_quality_score', 'N/A'):.2f}\n" if qm.get('base_quality_score') is not None else "Base Quantitative Score: N/A\n")
            f.write(f"LLM Qualitative Score: {qm.get('llm_qualitative_score', 'N/A'):.2f}\n" if qm.get('llm_qualitative_score') is not None else "LLM Qualitative Score: N/A\n")
            f.write(f"{'-'*10}\n")

            cat_order = ['profitability', 'accounting_quality', 'payout_efficiency', 'investment_quality']
            metric_fmt_order = {
                "profitability": ['roe', 'roa', 'operating_margin', 'gross_margin'],
                "accounting_quality": ['accruals_ratio', 'earnings_quality'],
                "payout_efficiency": ['dividend_payout', 'share_dilution'],
                "investment_quality": ['asset_growth', 'asset_turnover', 'investing_flow_efficiency', 'investing_flow_rate']
            }
            percent_metrics = ['roe', 'roa', 'operating_margin', 'gross_margin', 'dividend_payout', 'share_dilution', 'asset_growth', 'investing_flow_efficiency', 'investing_flow_rate', 'accruals_ratio']

            for cat in cat_order:
                if cat in qm and isinstance(qm[cat], dict):
                    f.write(f"{cat.replace('_', ' ').title()}:\n")
                    for metric in metric_fmt_order.get(cat, list(qm[cat].keys())):
                        if metric in qm[cat]:
                            val = qm[cat][metric]; title = metric.replace('_', ' ').title(); note = ""
                            if cat == 'payout_efficiency' and val is None: note = " (N/A - Data Unavailable)"

                            if val is None: f.write(f"  {title}: N/A{note}\n")
                            elif val == float('inf'): f.write(f"  {title}: Inf{note}\n")
                            elif metric in percent_metrics: f.write(f"  {title}: {val:.2%}{note}\n")
                            elif isinstance(val, (float, int)): f.write(f"  {title}: {val:.2f}x{note}\n" if metric in ['earnings_quality', 'asset_turnover'] else f"  {title}: {val:.2f}{note}\n")
                            else: f.write(f"  {title}: {val}{note}\n") # Should not happen if metrics are numeric
                    f.write("\n")

            if 'raw_values_latest' in qm and isinstance(qm['raw_values_latest'], dict):
                 f.write(f"{'-'*10}\nLatest Period Raw Values (Polygon /vX):\n")
                 raw_vals = qm['raw_values_latest']
                 raw_order = ['filing_date', 'period_end_date', 'revenue', 'net_income', 'operating_income', 'gross_profit', 'total_assets', 'total_assets_prev', 'equity', 'total_liabilities', 'net_cash_op', 'net_cash_investing', 'capex_proxy', 'interest_expense', 'dividends', 'shares_basic']
                 for key in raw_order:
                     if key in raw_vals:
                         val = raw_vals[key]; title = key.replace('_', ' ').title()
                         if val is None: f.write(f"  {title}: N/A\n")
                         elif isinstance(val, str) and re.match(r'\d{4}-\d{2}-\d{2}', val): f.write(f"  {title}: {val}\n") # Date string
                         elif isinstance(val, (int, float)) or (isinstance(val, str) and val.replace('.', '', 1).replace('-', '', 1).isdigit()):
                             try:
                                 f_val = float(val)
                                 fmt_str = f"{f_val:,.0f}" if key in ['shares_basic', 'dividends'] or abs(f_val) >= 10000 else f"{f_val:,.2f}"
                             except (ValueError, TypeError):
                                 fmt_str = str(val)
                             f.write(f"  {title}: {fmt_str}\n")
                         else: f.write(f"  {title}: {val}\n") # Other string values
        return filename
    except Exception as e:
        print(f"Error saving analysis file {filename}: {e}"); import traceback; traceback.print_exc()
        return f"SAVE_ERROR_{safe_company}_{datetime.now().strftime('%Y%m%d%H%M%S')}.txt"

# --- Main Execution ---
def main():
    print("--- Script Starting ---")
    # Critical API Key Checks
    if not Config.POLYGON_API_KEY: print("CRITICAL ERROR: POLYGON_API_KEY not set. Exiting."); return
    # Warnings for optional keys
    if not Config.OPENROUTER_API_KEY: print("Warning: OPENROUTER_API_KEY not set. LLM analysis will be skipped.")
    if not Config.FRED_API_KEY: print("Warning: FRED_API_KEY not set. FRED economic data will not be fetched.")
    if not Config.COMPANIES: print("Warning: No companies in Config.COMPANIES. Exiting."); return

    start_time = time.time()
    session = setup_http_session()

    fred_data_global = fetch_economic_data_fred(session) # Fetch once globally

    unique_companies = sorted(list(set(Config.COMPANIES)))
    print(f"Starting analysis for {len(unique_companies)} companies: {', '.join(unique_companies)}")
    print(f"LLM: {Config.MODEL_NAME if Config.OPENROUTER_API_KEY else 'N/A (Key Missing)'}, Detailed Logging: {Config.DETAILED_LOGGING}")
    print(f"Delays: Polygon API: {Config.REQUEST_DELAY}s, FRED API: {Config.FRED_REQUEST_DELAY}s, Inter-Company: {Config.INTER_COMPANY_DELAY}s")
    print(f"Max Workers: {Config.MAX_WORKERS}")

    results = []
    if Config.MAX_WORKERS <= 1 or len(unique_companies) == 1:
        print("Running analyses sequentially...")
        for i, company in enumerate(unique_companies):
            results.append(process_company(session, company, fred_data_global))
            if i < len(unique_companies) - 1: time.sleep(Config.INTER_COMPANY_DELAY)
    else:
        print(f"Running analyses concurrently (Max Workers: {Config.MAX_WORKERS})...")
        if 'polygon.io' in Config.POLYGON_BASE_URL and Config.REQUEST_DELAY * Config.MAX_WORKERS < 12: # 5 req/min = 12s per req
             print("WARNING: Concurrency with low Polygon delay may cause rate limits. Ensure total requests/min < 5.")
        with ThreadPoolExecutor(max_workers=Config.MAX_WORKERS) as executor:
            futures = [executor.submit(process_company, session, company, fred_data_global) for company in unique_companies]
            for i, future in enumerate(futures):
                try:
                    # Add a reasonable timeout (e.g., 10 minutes per company)
                    timeout_seconds = 600  # 10 minutes
                    results.append(future.result(timeout=timeout_seconds))
                    print(f"--- Completed analysis {i+1}/{len(unique_companies)} ---")
                except concurrent.futures.TimeoutError:
                    print(f"Error in concurrent task for company {unique_companies[i]}: Timed out after {timeout_seconds} seconds")
                    results.append(False)
                except Exception as e:
                    print(f"Error in concurrent task for company {unique_companies[i]}: {e}")
                    results.append(False)

    total_time = time.time() - start_time
    successful = sum(1 for r in results if r is True)
    print(f"\n--- Analysis Complete ---")
    print(f"Successfully completed {successful}/{len(unique_companies)} analyses.")
    if successful < len(unique_companies): print(f"Failed analyses: {len(unique_companies) - successful}")
    print(f"Total execution time: {total_time:.2f} seconds.\n--- Script Finished ---")

if __name__ == "__main__":
    main()